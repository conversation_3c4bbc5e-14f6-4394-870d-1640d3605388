package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.user.UserMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.ParseUsername;
import org.thingsboard.server.dao.util.imodel.response.annotations.ResponseEntity;

import java.util.Date;

/**
 * 巡检任务表单记录响应
 */
@Getter
@Setter
@ResponseEntity
public class SMCircuitTaskFormRecordResponse {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 巡检配置ID
     */
    private String inspectionConfigId;

    /**
     * 表单项ID
     */
    private String formItemId;

    /**
     * 巡检地点
     */
    private String location;

    /**
     * 巡检位置
     */
    private String position;

    /**
     * 检验内容
     */
    private String content;

    /**
     * 检验结果
     */
    private String result;

    /**
     * 结果描述
     */
    private String resultDescription;

    /**
     * 附件
     */
    private String attachments;

    /**
     * 检验时间
     */
    private Date checkTime;

    /**
     * 检验人员ID
     */
    @ParseUsername(withDepartment = true)
    private String checkUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 租户ID
     */
    private String tenantId;
}
