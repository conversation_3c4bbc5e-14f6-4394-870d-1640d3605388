package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.util.imodel.query.AdvancedPageableQueryEntity;

/**
 * 巡检任务表单记录分页查询请求
 */
@Getter
@Setter
public class SMCircuitTaskFormRecordPageRequest extends AdvancedPageableQueryEntity<SMCircuitTaskFormRecord, SMCircuitTaskFormRecordPageRequest> {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 巡检配置ID
     */
    private String inspectionConfigId;

    /**
     * 检验结果
     */
    private String result;

    /**
     * 巡检地点
     */
    private String location;

    /**
     * 巡检位置
     */
    private String position;

    /**
     * 检验人员ID
     */
    private String checkUserId;

    /**
     * 租户ID
     */
    private String tenantId;
}
