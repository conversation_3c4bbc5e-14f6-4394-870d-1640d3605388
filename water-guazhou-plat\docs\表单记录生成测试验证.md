# 表单记录生成测试验证

## 问题修复说明

### 问题描述
在生成巡检任务表单记录时出现错误：
```
ERROR: null value in column "task_code" violates not-null constraint
```

### 根本原因
在巡检计划生成任务的过程中，任务的`code`字段是在任务创建之后通过`getBatchCode()`方法设置的，但是在生成表单记录时使用的是任务对象中的`code`字段，此时该字段还是null。

### 修复方案
1. 使用已经获取的`codeList`来为表单记录提供正确的任务编号
2. 在表单记录生成方法中添加参数验证，确保必要字段不为空
3. 修改实体类ID注解为`@TableId(type = IdType.INPUT)`确保正确处理ID字段

## 修复后的代码流程

### 1. 巡检计划服务修改
```java
// 生成表单记录（如果巡检计划关联了巡检配置）
if (plan.getInspectionConfigId() != null && !plan.getInspectionConfigId().trim().isEmpty()) {
    try {
        CircuitSettings circuitSettings = circuitSettingsService.findById(plan.getInspectionConfigId());
        if (circuitSettings != null && circuitSettings.getFormConfig() != null && !circuitSettings.getFormConfig().trim().isEmpty()) {
            // 为每个任务生成表单记录，使用已获取的codeList
            for (int i = 0; i < tasks.size() && i < codeList.size(); i++) {
                SMCircuitTask task = tasks.get(i);
                String taskCode = codeList.get(i);  // 使用正确的任务编号
                
                formRecordService.generateFormRecords(
                    task.getId(),
                    taskCode,  // 确保不为null
                    plan.getInspectionConfigId(),
                    circuitSettings.getFormConfig(),
                    task.getTenantId()
                );
            }
        }
    } catch (Exception e) {
        System.err.println("生成表单记录失败: " + e.getMessage());
    }
}
```

### 2. 表单记录服务修改
```java
@Transactional
public int generateFormRecords(String taskId, String taskCode, String inspectionConfigId, String formConfig, String tenantId) {
    if (formConfig == null || formConfig.trim().isEmpty()) {
        return 0;
    }
    
    // 检查必要参数
    if (taskId == null || taskId.trim().isEmpty()) {
        throw new RuntimeException("任务ID不能为空");
    }
    if (taskCode == null || taskCode.trim().isEmpty()) {
        throw new RuntimeException("任务编号不能为空");
    }
    
    // ... 其余逻辑
}
```

### 3. 实体类修改
```java
@TableId(type = IdType.INPUT)  // 明确指定使用输入的ID值
private String id;
```

## 测试验证步骤

### 1. 准备测试数据
```sql
-- 运行测试数据脚本
\i water-guazhou-plat/sql/test_circuit_settings_data.sql
```

### 2. 创建巡检计划
1. 访问巡检计划制定页面
2. 选择区域和设备
3. 选择测试用的巡检配置
4. 创建计划

### 3. 生成巡检任务
1. 在巡检计划列表中选择刚创建的计划
2. 点击"生成任务"
3. 设置任务参数
4. 确认生成

### 4. 验证表单记录
```sql
-- 检查任务是否正确创建
SELECT id, code, name FROM sm_circuit_task 
WHERE plan_id = 'your_plan_id' 
ORDER BY create_time DESC;

-- 检查表单记录是否正确生成
SELECT 
    task_id, 
    task_code, 
    inspection_config_id, 
    location, 
    position, 
    content 
FROM sm_circuit_task_form_record 
WHERE task_id IN (
    SELECT id FROM sm_circuit_task WHERE plan_id = 'your_plan_id'
)
ORDER BY task_id, form_item_id;

-- 验证数据完整性
SELECT 
    t.id as task_id,
    t.code as task_code,
    COUNT(f.id) as form_record_count
FROM sm_circuit_task t
LEFT JOIN sm_circuit_task_form_record f ON t.id = f.task_id
WHERE t.plan_id = 'your_plan_id'
GROUP BY t.id, t.code;
```

## 预期结果

### 1. 任务创建成功
- 任务表中应该有新创建的任务记录
- 每个任务都有正确的code字段值

### 2. 表单记录生成成功
- 表单记录表中应该有对应的记录
- 每条记录的task_code字段不为空
- 记录数量应该等于：任务数量 × 表单配置项数量

### 3. 数据关联正确
- 表单记录的task_id正确关联到任务
- 表单记录的task_code与任务的code字段一致
- 表单记录的inspection_config_id正确关联到巡检配置

## 错误排查

### 如果仍然出现task_code为null的错误

1. **检查codeList是否正确生成**
   ```java
   System.out.println("codeList size: " + codeList.size());
   System.out.println("tasks size: " + tasks.size());
   for (int i = 0; i < codeList.size(); i++) {
       System.out.println("Task " + i + " code: " + codeList.get(i));
   }
   ```

2. **检查getBatchCode方法**
   - 确保该方法返回正确的任务编号列表
   - 验证返回的列表顺序与tasks列表一致

3. **检查任务创建过程**
   - 确保任务在数据库中正确保存
   - 验证任务的code字段是否在数据库中正确设置

### 如果出现其他字段为null的错误

1. **检查必要字段设置**
   ```java
   // 确保所有必要字段都已设置
   record.setId(java.util.UUID.randomUUID().toString().replace("-", ""));
   record.setTaskId(taskId);
   record.setTaskCode(taskCode);
   record.setInspectionConfigId(inspectionConfigId);
   record.setFormItemId(String.valueOf(i));
   record.setResult("NOT_CHECKED");
   record.setCreateTime(new Date());
   record.setTenantId(tenantId);
   ```

2. **检查数据库约束**
   ```sql
   -- 查看表结构和约束
   \d sm_circuit_task_form_record
   
   -- 检查哪些字段有NOT NULL约束
   SELECT column_name, is_nullable 
   FROM information_schema.columns 
   WHERE table_name = 'sm_circuit_task_form_record';
   ```

## 性能考虑

### 批量处理优化
如果任务数量很大，考虑批量处理：

```java
// 分批处理表单记录生成
int batchSize = 100;
for (int start = 0; start < tasks.size(); start += batchSize) {
    int end = Math.min(start + batchSize, tasks.size());
    List<SMCircuitTask> batchTasks = tasks.subList(start, end);
    List<String> batchCodes = codeList.subList(start, Math.min(end, codeList.size()));
    
    // 处理这一批任务
    for (int i = 0; i < batchTasks.size() && i < batchCodes.size(); i++) {
        // 生成表单记录
    }
}
```

### 事务管理
确保表单记录生成失败不影响任务创建：

```java
try {
    // 表单记录生成逻辑
} catch (Exception e) {
    // 记录错误但不抛出异常，避免影响主流程
    log.error("生成表单记录失败", e);
}
```

通过以上修复和验证步骤，应该能够解决task_code为null的问题，确保表单记录正确生成。
