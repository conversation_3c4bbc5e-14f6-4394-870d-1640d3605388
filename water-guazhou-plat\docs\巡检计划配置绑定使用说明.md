# 巡检计划配置绑定使用说明

## 功能概述

本功能允许在创建巡检计划时绑定巡检配置，实现巡检任务的标准化表单填写。

## 使用步骤

### 1. 创建巡检配置

首先需要在巡检配置管理页面创建巡检配置：

1. 进入巡检配置管理页面
2. 点击"新增"按钮
3. 填写配置信息：
   - **配置名称**：如"水厂巡检配置"
   - **配置编码**：如"WATER_PLANT_001"
   - **配置类型**：选择对应类型（管网、泵站、其他）
   - **状态**：选择"启用"
4. 配置表单内容：
   - 添加巡检地点、位置、检验内容
   - 可添加多个检查项
5. 保存配置

### 2. 创建巡检计划并绑定配置

在巡检计划制定页面：

1. 访问 `/arcMap/Inspection/Plans/InspectPlan` 页面
2. 填写基本信息：
   - 选择区域
   - 选择设备类别
   - 设置计划参数（常规/临时、是否需要反馈、进行方式、计划周期）
3. 输入计划名称
4. **选择巡检配置**：
   - 在"巡检配置"下拉框中选择已创建的配置
   - 下拉框显示格式：`配置名称 (配置编码)`
   - 此字段为可选，不选择则不会生成表单记录
5. 填写备注
6. 点击"确定"保存

### 3. 生成巡检任务

当巡检计划生成任务时：

1. 系统会检查计划是否关联了巡检配置
2. 如果关联了配置，会自动为每个任务生成对应的表单记录
3. 表单记录包含配置中定义的所有检查项

### 4. 执行巡检任务

工作人员在巡检任务详情页面：

1. 点击"巡检表单"Tab
2. 查看所有需要检查的项目
3. 对每个项目点击"填写"按钮
4. 选择检验结果（正常/异常）
5. 填写结果描述
6. 上传相关附件
7. 保存填写结果

### 5. 任务完成

- 只有当所有表单项都已填写完成时，任务才能标记为完成
- 系统会自动检查表单完成状态

## 界面说明

### 巡检计划制定页面

在巡检计划制定页面中，新增了"巡检配置"字段：

```
┌─────────────────────────────────────┐
│ 区域: [下拉选择]                      │
│ 设备类别: [多选下拉]                   │
│ 是否常规计划: ○常规 ○临时              │
│ 是否需要反馈: ○需要 ○仅到位            │
│ 进行方式: ○车巡 ○步行                 │
│ 计划周期: [下拉选择]                   │
│ 计划名称: [文本输入]                   │
│ 巡检配置: [下拉选择] (可选)             │  ← 新增字段
│ 备注: [文本域]                        │
│                                     │
│ [重置]              [确定]           │
└─────────────────────────────────────┘
```

### 巡检配置下拉框

- 只显示状态为"启用"的巡检配置
- 显示格式：`配置名称 (配置编码)`
- 支持清空选择
- 可选字段，不选择不影响计划创建

## 数据流转

```
巡检配置 (CircuitSettings)
    ↓ (inspectionConfigId)
巡检计划 (SMCircuitPlan)
    ↓ (生成任务时)
巡检任务 (SMCircuitTask)
    ↓ (自动生成)
表单记录 (SMCircuitTaskFormRecord)
    ↓ (工作人员填写)
检验结果
```

## 注意事项

1. **配置状态**：只有启用状态的巡检配置才会在下拉框中显示
2. **可选绑定**：巡检配置是可选的，不绑定不影响计划创建
3. **表单生成**：只有绑定了巡检配置的计划才会生成表单记录
4. **权限控制**：需要确保用户有查看巡检配置的权限
5. **数据完整性**：删除巡检配置前需要检查是否有计划在使用

## 测试验证

可以使用测试页面验证功能：

1. **综合测试页面**：`/test/InspectionFormTest`
   - 测试巡检配置的查看和解析
   - 测试计划配置绑定
   - 测试表单记录生成

2. **数据库验证**：
   ```sql
   -- 查看计划是否正确关联配置
   SELECT id, name, inspection_config_id 
   FROM sm_circuit_plan 
   WHERE inspection_config_id IS NOT NULL;
   
   -- 查看表单记录是否正确生成
   SELECT task_id, inspection_config_id, COUNT(*) as form_count
   FROM sm_circuit_task_form_record 
   GROUP BY task_id, inspection_config_id;
   ```

## 故障排除

### 问题1：下拉框中没有巡检配置选项

**原因**：
- 没有创建巡检配置
- 巡检配置状态为"停用"
- 网络请求失败

**解决方法**：
1. 检查巡检配置管理页面是否有启用的配置
2. 检查浏览器控制台是否有错误信息
3. 检查API接口是否正常

### 问题2：选择配置后没有生成表单记录

**原因**：
- 巡检配置的formConfig字段为空
- JSON格式错误
- 任务生成过程中出现异常

**解决方法**：
1. 检查巡检配置的表单配置是否正确
2. 检查后端日志是否有异常信息
3. 验证JSON格式是否正确

### 问题3：表单记录显示异常

**原因**：
- 前端组件渲染问题
- 数据格式不匹配
- 权限问题

**解决方法**：
1. 检查浏览器控制台错误
2. 验证API返回数据格式
3. 检查用户权限设置

这个功能现在已经完整实现，可以在巡检计划制定时选择巡检配置，实现标准化的巡检表单管理。
