# 后端表单完成率计算需求

## 需求概述

在巡检任务列表中需要显示每个任务的表单完成率，该数据需要后端计算并返回给前端。

## 计算逻辑

### 表单完成率定义
```
表单完成率 = (已检查的表单记录数 / 总表单记录数) × 100%
```

### 判断标准
- **已检查**：`sm_circuit_task_form_record.result != 'NOT_CHECKED'`
- **未检查**：`sm_circuit_task_form_record.result = 'NOT_CHECKED'`

## 后端实现建议

### 1. 在任务查询时计算完成率

#### 修改任务响应DTO
```java
// SMCircuitTaskResponse.java 或相应的响应类
public class SMCircuitTaskResponse {
    // 现有字段...
    private String id;
    private String code;
    private String name;
    private String status;
    // ...
    
    // 新增字段
    private Integer formCompletionRate; // 表单完成率（百分比）
    private Integer totalFormRecords;   // 总表单记录数
    private Integer completedFormRecords; // 已完成表单记录数
    
    // getter/setter...
}
```

### 2. 在Service层添加计算逻辑

#### 方案一：在查询任务时联表计算
```java
// SMCircuitTaskServiceImpl.java
public PageData<SMCircuitTaskResponse> findByPage(SMCircuitTaskPageRequest request) {
    // 查询任务列表
    IPage<SMCircuitTask> page = mapper.findByPage(request);
    
    List<SMCircuitTaskResponse> responseList = page.getRecords().stream()
        .map(task -> {
            SMCircuitTaskResponse response = convertToResponse(task);
            
            // 计算表单完成率
            FormCompletionStats stats = calculateFormCompletionRate(task.getCode());
            response.setFormCompletionRate(stats.getCompletionRate());
            response.setTotalFormRecords(stats.getTotalRecords());
            response.setCompletedFormRecords(stats.getCompletedRecords());
            
            return response;
        })
        .collect(Collectors.toList());
    
    return new PageData<>(responseList, page.getTotal());
}

private FormCompletionStats calculateFormCompletionRate(String taskCode) {
    // 查询该任务的所有表单记录
    List<SMCircuitTaskFormRecord> records = formRecordService.findByTaskCode(taskCode);
    
    int totalRecords = records.size();
    int completedRecords = (int) records.stream()
        .filter(record -> !"NOT_CHECKED".equals(record.getResult()))
        .count();
    
    int completionRate = totalRecords > 0 
        ? Math.round((float) completedRecords / totalRecords * 100)
        : 0;
    
    return new FormCompletionStats(totalRecords, completedRecords, completionRate);
}
```

#### 方案二：在Mapper层使用SQL计算
```xml
<!-- SMCircuitTaskMapper.xml -->
<select id="findByPageWithFormStats" resultType="SMCircuitTaskResponse">
    SELECT 
        t.*,
        COALESCE(f.total_records, 0) as totalFormRecords,
        COALESCE(f.completed_records, 0) as completedFormRecords,
        CASE 
            WHEN COALESCE(f.total_records, 0) = 0 THEN 0
            ELSE ROUND(COALESCE(f.completed_records, 0) * 100.0 / f.total_records)
        END as formCompletionRate
    FROM sm_circuit_task t
    LEFT JOIN (
        SELECT 
            task_code,
            COUNT(*) as total_records,
            COUNT(CASE WHEN result != 'NOT_CHECKED' THEN 1 END) as completed_records
        FROM sm_circuit_task_form_record 
        GROUP BY task_code
    ) f ON t.code = f.task_code
    WHERE 1=1
    <if test="request.status != null and request.status != ''">
        AND t.status IN 
        <foreach collection="request.status" item="status" open="(" close=")" separator=",">
            #{status}
        </foreach>
    </if>
    <!-- 其他查询条件... -->
    ORDER BY t.create_time DESC
</select>
```

### 3. 创建统计数据类
```java
// FormCompletionStats.java
public class FormCompletionStats {
    private Integer totalRecords;      // 总记录数
    private Integer completedRecords;  // 已完成记录数
    private Integer completionRate;    // 完成率（百分比）
    
    public FormCompletionStats(Integer totalRecords, Integer completedRecords, Integer completionRate) {
        this.totalRecords = totalRecords;
        this.completedRecords = completedRecords;
        this.completionRate = completionRate;
    }
    
    // getter/setter...
}
```

## 前端接收格式

### API响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": "task_id",
        "code": "202506090008",
        "name": "水厂日常巡检",
        "status": "APPROVED",
        "receiveUserName": "张三",
        "formCompletionRate": 75,        // 表单完成率
        "totalFormRecords": 8,           // 总表单记录数
        "completedFormRecords": 6,       // 已完成表单记录数
        "createTime": 1749439807060
      }
    ],
    "total": 100
  }
}
```

### 前端显示逻辑
```javascript
// 在表格列中显示
{
  minWidth: 120,
  label: '表单完成率',
  prop: 'formCompletionRate',
  formatter: (row: any) => {
    if (row.formCompletionRate !== undefined && row.formCompletionRate !== null) {
      return `${row.formCompletionRate}%`
    }
    return '待计算'
  }
}
```

## 性能考虑

### 1. 缓存策略
```java
// 使用Redis缓存表单完成率
@Cacheable(value = "form_completion_rate", key = "#taskCode")
public FormCompletionStats getFormCompletionRate(String taskCode) {
    // 计算逻辑
}

// 当表单记录更新时清除缓存
@CacheEvict(value = "form_completion_rate", key = "#taskCode")
public void updateFormRecord(String taskCode, SMCircuitTaskFormRecord record) {
    // 更新逻辑
}
```

### 2. 批量计算
```java
// 批量计算多个任务的完成率
public Map<String, FormCompletionStats> batchCalculateFormCompletionRate(List<String> taskCodes) {
    // 一次查询获取所有相关的表单记录
    List<SMCircuitTaskFormRecord> allRecords = formRecordService.findByTaskCodes(taskCodes);
    
    // 按任务编号分组计算
    return allRecords.stream()
        .collect(Collectors.groupingBy(SMCircuitTaskFormRecord::getTaskCode))
        .entrySet().stream()
        .collect(Collectors.toMap(
            Map.Entry::getKey,
            entry -> calculateStats(entry.getValue())
        ));
}
```

### 3. 数据库索引
```sql
-- 为表单记录表添加索引
CREATE INDEX idx_form_record_task_code_result ON sm_circuit_task_form_record(task_code, result);
```

## 测试验证

### 1. 单元测试
```java
@Test
public void testFormCompletionRateCalculation() {
    // 准备测试数据
    String taskCode = "TEST_TASK_001";
    
    // 模拟表单记录：总共5条，已完成3条
    List<SMCircuitTaskFormRecord> records = Arrays.asList(
        createRecord(taskCode, "NORMAL"),      // 已完成
        createRecord(taskCode, "ABNORMAL"),    // 已完成
        createRecord(taskCode, "NORMAL"),      // 已完成
        createRecord(taskCode, "NOT_CHECKED"), // 未完成
        createRecord(taskCode, "NOT_CHECKED")  // 未完成
    );
    
    // 执行计算
    FormCompletionStats stats = calculateFormCompletionRate(taskCode);
    
    // 验证结果
    assertEquals(5, stats.getTotalRecords());
    assertEquals(3, stats.getCompletedRecords());
    assertEquals(60, stats.getCompletionRate()); // 3/5 * 100 = 60%
}
```

### 2. 集成测试
```java
@Test
public void testTaskListWithFormCompletionRate() {
    // 创建测试任务和表单记录
    SMCircuitTask task = createTestTask();
    createTestFormRecords(task.getCode());
    
    // 查询任务列表
    SMCircuitTaskPageRequest request = new SMCircuitTaskPageRequest();
    PageData<SMCircuitTaskResponse> result = taskService.findByPage(request);
    
    // 验证返回结果包含完成率
    assertNotNull(result.getData());
    assertTrue(result.getData().size() > 0);
    
    SMCircuitTaskResponse taskResponse = result.getData().get(0);
    assertNotNull(taskResponse.getFormCompletionRate());
    assertTrue(taskResponse.getFormCompletionRate() >= 0);
    assertTrue(taskResponse.getFormCompletionRate() <= 100);
}
```

## 实施步骤

### 阶段1：基础实现
1. 修改任务响应DTO，添加完成率字段
2. 实现基础的完成率计算逻辑
3. 修改任务查询接口，返回完成率数据

### 阶段2：性能优化
1. 添加数据库索引
2. 实现缓存机制
3. 优化SQL查询性能

### 阶段3：功能完善
1. 添加批量计算功能
2. 实现实时更新机制
3. 添加监控和日志

通过以上实现，前端就可以直接显示后端计算好的表单完成率，无需在前端进行复杂的数据处理。
