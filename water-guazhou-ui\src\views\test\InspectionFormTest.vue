<template>
  <div class="inspection-form-test">
    <h2>巡检表单功能测试</h2>
    
    <!-- 测试步骤说明 -->
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>测试步骤</span>
      </template>
      <ol>
        <li>确保数据库中有测试用的巡检配置数据（运行 test_circuit_settings_data.sql）</li>
        <li>创建巡检计划并关联巡检配置</li>
        <li>生成巡检任务</li>
        <li>在任务详情页面查看和填写巡检表单</li>
      </ol>
    </el-card>

    <!-- 巡检配置测试 -->
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>1. 巡检配置测试</span>
      </template>
      
      <el-form :model="configForm" label-width="120px">
        <el-form-item label="配置ID">
          <el-select v-model="configForm.configId" placeholder="请选择巡检配置">
            <el-option 
              v-for="config in configList" 
              :key="config.id" 
              :label="config.name" 
              :value="config.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadConfigDetail">查看配置详情</el-button>
          <el-button @click="loadConfigList">刷新配置列表</el-button>
        </el-form-item>
      </el-form>

      <!-- 配置详情显示 -->
      <div v-if="configDetail">
        <h4>配置详情</h4>
        <p><strong>名称：</strong>{{ configDetail.name }}</p>
        <p><strong>编码：</strong>{{ configDetail.code }}</p>
        <p><strong>类型：</strong>{{ configDetail.type }}</p>
        
        <h4>表单配置</h4>
        <el-table :data="formConfigItems" border>
          <el-table-column prop="location" label="巡检地点"></el-table-column>
          <el-table-column prop="position" label="巡检位置"></el-table-column>
          <el-table-column prop="content" label="检验内容"></el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 表单记录测试 -->
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>2. 表单记录测试</span>
      </template>
      
      <el-form :model="recordForm" label-width="120px">
        <el-form-item label="任务ID">
          <el-input v-model="recordForm.taskId" placeholder="请输入任务ID"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadFormRecords">查看表单记录</el-button>
          <el-button type="success" @click="generateTestRecords">生成测试记录</el-button>
        </el-form-item>
      </el-form>

      <!-- 表单记录列表 -->
      <div v-if="formRecords.length > 0">
        <h4>表单记录列表</h4>
        <el-table :data="formRecords" border>
          <el-table-column prop="location" label="巡检地点"></el-table-column>
          <el-table-column prop="position" label="巡检位置"></el-table-column>
          <el-table-column prop="content" label="检验内容"></el-table-column>
          <el-table-column prop="result" label="检验结果">
            <template #default="{ row }">
              <el-tag :type="getResultType(row.result)">
                {{ getResultText(row.result) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="resultDescription" label="结果描述"></el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button size="small" @click="editRecord(row)">填写</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog v-model="dialogVisible" title="填写巡检表单" width="600px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="巡检地点">
          <el-input v-model="editForm.location" readonly></el-input>
        </el-form-item>
        <el-form-item label="巡检位置">
          <el-input v-model="editForm.position" readonly></el-input>
        </el-form-item>
        <el-form-item label="检验内容">
          <el-input v-model="editForm.content" type="textarea" readonly></el-input>
        </el-form-item>
        <el-form-item label="检验结果">
          <el-radio-group v-model="editForm.result">
            <el-radio label="NORMAL">正常</el-radio>
            <el-radio label="ABNORMAL">异常</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="结果描述">
          <el-input v-model="editForm.resultDescription" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="附件">
          <el-input v-model="editForm.attachments" placeholder="附件路径，多个用逗号分隔"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveRecord">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getCircuitSettingsList, getCircuitSettingsById } from '@/api/CircuitSettings/circuitSettings'
import { 
  getFormRecordsByTaskId, 
  updateCheckResult 
} from '@/api/smartManagement/circuitTaskFormRecord'

// 配置相关
const configForm = reactive({
  configId: ''
})

const configList = ref([])
const configDetail = ref(null)
const formConfigItems = ref([])

// 表单记录相关
const recordForm = reactive({
  taskId: ''
})

const formRecords = ref([])
const dialogVisible = ref(false)
const editForm = reactive({
  id: '',
  location: '',
  position: '',
  content: '',
  result: '',
  resultDescription: '',
  attachments: ''
})

// 加载配置列表
const loadConfigList = async () => {
  try {
    const res = await getCircuitSettingsList({ page: 1, size: 100 })
    configList.value = res.data?.data?.data || []
    ElMessage.success('配置列表加载成功')
  } catch (error) {
    console.error('加载配置列表失败:', error)
    ElMessage.error('加载配置列表失败')
  }
}

// 加载配置详情
const loadConfigDetail = async () => {
  if (!configForm.configId) {
    ElMessage.warning('请选择配置')
    return
  }
  
  try {
    const res = await getCircuitSettingsById(configForm.configId)
    configDetail.value = res.data?.data || res.data
    
    // 解析表单配置
    if (configDetail.value?.formConfig) {
      try {
        formConfigItems.value = JSON.parse(configDetail.value.formConfig)
      } catch (e) {
        console.error('解析表单配置失败:', e)
        formConfigItems.value = []
      }
    }
    
    ElMessage.success('配置详情加载成功')
  } catch (error) {
    console.error('加载配置详情失败:', error)
    ElMessage.error('加载配置详情失败')
  }
}

// 加载表单记录
const loadFormRecords = async () => {
  if (!recordForm.taskId) {
    ElMessage.warning('请输入任务ID')
    return
  }
  
  try {
    const res = await getFormRecordsByTaskId(recordForm.taskId)
    formRecords.value = res.data || []
    ElMessage.success('表单记录加载成功')
  } catch (error) {
    console.error('加载表单记录失败:', error)
    ElMessage.error('加载表单记录失败')
  }
}

// 生成测试记录（模拟）
const generateTestRecords = () => {
  if (!formConfigItems.value.length) {
    ElMessage.warning('请先选择配置并查看详情')
    return
  }
  
  // 模拟生成表单记录
  formRecords.value = formConfigItems.value.map((item, index) => ({
    id: `test_record_${index}`,
    taskId: recordForm.taskId || 'test_task_001',
    location: item.location,
    position: item.position,
    content: item.content,
    result: 'NOT_CHECKED',
    resultDescription: '',
    attachments: ''
  }))
  
  ElMessage.success('测试记录生成成功')
}

// 编辑记录
const editRecord = (row: any) => {
  Object.assign(editForm, row)
  dialogVisible.value = true
}

// 保存记录
const saveRecord = async () => {
  try {
    await updateCheckResult(editForm.id, {
      result: editForm.result,
      resultDescription: editForm.resultDescription,
      attachments: editForm.attachments,
      checkUserId: 'test_user'
    })
    ElMessage.success('保存成功')
    dialogVisible.value = false
    loadFormRecords() // 重新加载数据
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 获取结果类型
const getResultType = (result: string) => {
  const typeMap = {
    'NORMAL': 'success',
    'ABNORMAL': 'danger',
    'NOT_CHECKED': 'info'
  }
  return typeMap[result] || 'info'
}

// 获取结果文本
const getResultText = (result: string) => {
  const textMap = {
    'NORMAL': '正常',
    'ABNORMAL': '异常',
    'NOT_CHECKED': '未检查'
  }
  return textMap[result] || result
}

onMounted(() => {
  loadConfigList()
})
</script>

<style lang="scss" scoped>
.inspection-form-test {
  padding: 20px;
}

h4 {
  margin: 16px 0 8px 0;
  color: #409eff;
}
</style>
