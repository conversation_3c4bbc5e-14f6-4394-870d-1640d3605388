package org.thingsboard.server.controller.smartManagement.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.plan.SMCircuitTaskFormRecordService;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskFormRecordPageRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.List;

/**
 * 巡检任务表单记录控制器
 */
@IStarController2
@RequestMapping("/api/sm/circuitTaskFormRecord")
public class SMCircuitTaskFormRecordController extends BaseController {

    @Autowired
    private SMCircuitTaskFormRecordService service;

    /**
     * 分页查询表单记录
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    @GetMapping
    public IPage<SMCircuitTaskFormRecord> findAllConditional(SMCircuitTaskFormRecordPageRequest request) throws ThingsboardException {
        // 设置租户ID
        request.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return service.findAllConditional(request);
    }

    /**
     * 根据任务ID查询表单记录
     *
     * @param taskId 任务ID
     * @return 表单记录列表
     */
    @GetMapping("/task/{taskId}")
    public List<SMCircuitTaskFormRecord> findByTaskId(@PathVariable String taskId) {
        return service.findByTaskId(taskId);
    }

    /**
     * 根据任务编号查询表单记录
     *
     * @param taskCode 任务编号
     * @return 表单记录列表
     */
    @GetMapping("/taskCode/{taskCode}")
    public List<SMCircuitTaskFormRecord> findByTaskCode(@PathVariable String taskCode) {
        return service.findByTaskCode(taskCode);
    }

    /**
     * 根据ID查询表单记录
     *
     * @param id 记录ID
     * @return 表单记录
     */
    @GetMapping("/{id}")
    public SMCircuitTaskFormRecord findById(@PathVariable String id) {
        return service.findById(id);
    }

    /**
     * 保存表单记录
     *
     * @param entity 表单记录实体
     * @return 保存的实体
     */
    @PostMapping
    public SMCircuitTaskFormRecord save(@RequestBody SMCircuitTaskFormRecord entity) throws ThingsboardException {
        // 设置租户ID
        entity.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return service.save(entity);
    }

    /**
     * 更新表单记录
     *
     * @param entity 表单记录实体
     * @param id 记录ID
     * @return 是否更新成功
     */
    @PostMapping("/{id}")
    public boolean update(@RequestBody SMCircuitTaskFormRecord entity, @PathVariable String id) {
        entity.setId(id);
        return service.update(entity);
    }

    /**
     * 删除表单记录
     *
     * @param id 记录ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

    /**
     * 更新检验结果
     *
     * @param id 记录ID
     * @param record 更新的记录信息
     * @return 是否更新成功
     */
    @PostMapping("/{id}/checkResult")
    public boolean updateCheckResult(@PathVariable String id, @RequestBody SMCircuitTaskFormRecord record) {
        return service.updateCheckResult(
            id,
            record.getResult(),
            record.getResultDescription(),
            record.getAttachments(),
            record.getCheckUserId()
        );
    }

    /**
     * 批量更新检验结果
     *
     * @param records 表单记录列表
     * @return 是否更新成功
     */
    @PostMapping("/batchUpdate")
    public boolean batchUpdateCheckResult(@RequestBody List<SMCircuitTaskFormRecord> records) {
        return service.batchUpdateCheckResult(records);
    }

    /**
     * 检查任务表单是否全部完成
     *
     * @param taskId 任务ID
     * @return 是否全部完成
     */
    @GetMapping("/task/{taskId}/completed")
    public boolean isTaskFormCompleted(@PathVariable String taskId) {
        return service.isTaskFormCompleted(taskId);
    }
}
