package org.thingsboard.server.controller.smartManagement.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecordResponse;
import org.thingsboard.server.dao.plan.SMCircuitTaskFormRecordService;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskFormRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskFormRecordSaveRequest;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

/**
 * 巡检任务表单记录控制器
 */
@IStarController
@RequestMapping("/api/sm/circuitTaskFormRecord")
public class SMCircuitTaskFormRecordController extends BaseController {

    @Autowired
    private SMCircuitTaskFormRecordService service;

    /**
     * 分页查询表单记录
     *
     * @param request 分页查询请求
     * @return 分页结果
     */
    @GetMapping
    public IPage<SMCircuitTaskFormRecordResponse> findAllConditional(SMCircuitTaskFormRecordPageRequest request) {
        return service.findAllConditional(request);
    }

    /**
     * 根据任务ID查询表单记录
     *
     * @param taskId 任务ID
     * @return 表单记录列表
     */
    @GetMapping("/task/{taskId}")
    public List<SMCircuitTaskFormRecordResponse> findByTaskId(@PathVariable String taskId) {
        return service.findByTaskId(taskId);
    }

    /**
     * 根据任务编号查询表单记录
     *
     * @param taskCode 任务编号
     * @return 表单记录列表
     */
    @GetMapping("/taskCode/{taskCode}")
    public List<SMCircuitTaskFormRecordResponse> findByTaskCode(@PathVariable String taskCode) {
        return service.findByTaskCode(taskCode);
    }

    /**
     * 保存表单记录
     *
     * @param req 保存请求
     * @return 保存的实体
     */
    @PostMapping
    public SMCircuitTaskFormRecord save(@RequestBody SMCircuitTaskFormRecordSaveRequest req) {
        return service.save(req);
    }

    /**
     * 更新表单记录
     *
     * @param req 更新请求
     * @param id 记录ID
     * @return 是否更新成功
     */
    @PatchMapping("/{id}")
    public boolean edit(@RequestBody SMCircuitTaskFormRecordSaveRequest req, @PathVariable String id) {
        return service.update(req.update(id));
    }

    /**
     * 删除表单记录
     *
     * @param id 记录ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable String id) {
        return service.delete(id);
    }

    /**
     * 批量删除表单记录
     *
     * @param idList ID列表
     * @return 是否删除成功
     */
    @DeleteMapping
    public boolean deleteAll(@RequestBody List<String> idList) {
        return service.deleteAll(idList);
    }

    /**
     * 更新检验结果
     *
     * @param id 记录ID
     * @param record 更新的记录信息
     * @return 是否更新成功
     */
    @PostMapping("/{id}/checkResult")
    public boolean updateCheckResult(@PathVariable String id, @RequestBody SMCircuitTaskFormRecord record) {
        return service.updateCheckResult(
            id,
            record.getResult(),
            record.getResultDescription(),
            record.getAttachments(),
            record.getCheckUserId()
        );
    }

    /**
     * 批量更新检验结果
     *
     * @param records 表单记录列表
     * @return 是否更新成功
     */
    @PostMapping("/batchUpdate")
    public boolean batchUpdateCheckResult(@RequestBody List<SMCircuitTaskFormRecord> records) {
        return service.batchUpdateCheckResult(records);
    }

    /**
     * 检查任务表单是否全部完成
     *
     * @param taskId 任务ID
     * @return 是否全部完成
     */
    @GetMapping("/task/{taskId}/completed")
    public boolean isTaskFormCompleted(@PathVariable String taskId) {
        return service.isTaskFormCompleted(taskId);
    }
}
