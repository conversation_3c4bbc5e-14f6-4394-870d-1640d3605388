package org.thingsboard.server.controller.smartManagement.plan;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.controller.BaseController;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.plan.SMCircuitTaskFormRecordService;
import org.thingsboard.server.utils.imodel.annotations.IStarController;

import java.util.List;

/**
 * 巡检任务表单记录控制器
 */
@IStarController
@RequestMapping("/api/sm/circuitTaskFormRecord")
public class SMCircuitTaskFormRecordController extends BaseController {

    @Autowired
    private SMCircuitTaskFormRecordService service;

    /**
     * 根据任务ID查询表单记录
     *
     * @param taskId 任务ID
     * @return 表单记录列表
     */
    @GetMapping("/task/{taskId}")
    public List<SMCircuitTaskFormRecord> findByTaskId(@PathVariable String taskId) {
        return service.findByTaskId(taskId);
    }

    /**
     * 根据任务编号查询表单记录
     *
     * @param taskCode 任务编号
     * @return 表单记录列表
     */
    @GetMapping("/taskCode/{taskCode}")
    public List<SMCircuitTaskFormRecord> findByTaskCode(@PathVariable String taskCode) {
        return service.findByTaskCode(taskCode);
    }

    /**
     * 更新检验结果
     *
     * @param id 记录ID
     * @param record 更新的记录信息
     * @return 是否更新成功
     */
    @PatchMapping("/{id}")
    public boolean updateCheckResult(@PathVariable String id, @RequestBody SMCircuitTaskFormRecord record) {
        return service.updateCheckResult(
            id,
            record.getResult(),
            record.getResultDescription(),
            record.getAttachments(),
            record.getCheckUserId()
        );
    }

    /**
     * 批量更新检验结果
     *
     * @param records 表单记录列表
     * @return 是否更新成功
     */
    @PostMapping("/batchUpdate")
    public boolean batchUpdateCheckResult(@RequestBody List<SMCircuitTaskFormRecord> records) {
        return service.batchUpdateCheckResult(records);
    }

    /**
     * 检查任务表单是否全部完成
     *
     * @param taskId 任务ID
     * @return 是否全部完成
     */
    @GetMapping("/task/{taskId}/completed")
    public boolean isTaskFormCompleted(@PathVariable String taskId) {
        return service.isTaskFormCompleted(taskId);
    }
}
