<template>
  <div class="inspection-feedback">
    <Search
      ref="refSearch"
      :config="SearchConfig"
      style="margin-bottom: 8px; padding: 12px; background-color: #fff; border-radius: 4px;"
    ></Search>
    <div class="table-box">
      <FormTable :config="TableConfig"></FormTable>
    </div>

    <!-- 反馈详情弹窗 -->
    <FeedbackDetailDialog
      v-model="dialogVisible"
      :task-id="selectedTaskId"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, shallowRef } from 'vue'
import { View, Document, Download } from '@element-plus/icons-vue'
import Search from '@/components/Form/Search.vue'
import FormTable from '@/components/Form/FormTable.vue'
import FeedbackDetailDialog from './components/FeedbackDetailDialog.vue'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils/DateFormatter'
import { GetPatrolTaskList } from '@/api/patrol/gis/plan'
import { PatrolTaskStatusConfig } from '@/views/arcMap/Inspection/config'

// 搜索配置
const SearchConfig = reactive<ISearch>({
  labelWidth: 80,
  filters: [
    {
      type: 'input',
      label: '任务编号',
      field: 'keyword'
    },
    {
      type: 'input',
      label: '任务名称',
      field: 'taskName'
    },
    {
      type: 'select',
      label: '任务状态',
      field: 'status',
      options: [
        { label: '已完成', value: 'APPROVED' },
        { label: '待审核', value: 'VERIFY' },
        { label: '已驳回', value: 'REJECTED' }
      ]
    },
    {
      type: 'input',
      label: '执行人员',
      field: 'receiveUserId'
    },
    {
      type: 'daterange',
      label: '完成时间',
      field: 'completeTime',
      format: 'YYYY-MM-DD'
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          type: 'primary',
          click: () => refreshData()
        },
        {
          perm: true,
          text: '重置',
          type: 'default',
          click: () => {
            refSearch.value?.resetForm()
            refreshData()
          }
        }
      ]
    }
  ]
})

// 表格配置
const TableConfig = reactive<ITable>({
  title: '巡检反馈列表',
  height: 'calc(100vh - 180px)',
  indexVisible: true,
  columns: [
    { minWidth: 120, label: '任务编号', prop: 'code' },
    { minWidth: 150, label: '任务名称', prop: 'name' },
    { minWidth: 120, label: '执行人员', prop: 'receiveUserName' },
    { minWidth: 120, label: '协作人员', prop: 'collaborateUserName' },
    {
      minWidth: 160,
      label: '开始时间',
      prop: 'beginTime',
      formatter: (row: any) => formatDate(row.beginTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      minWidth: 160,
      label: '结束时间',
      prop: 'endTime',
      formatter: (row: any) => formatDate(row.endTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      minWidth: 100,
      label: '任务状态',
      prop: 'status',
      formatter: (row: any) => {
        const config = PatrolTaskStatusConfig[row.status as keyof typeof PatrolTaskStatusConfig]
        return config ? config.text : row.status
      }
    },
    {
      minWidth: 120,
      label: '反馈率',
      prop: 'feedbackRate',
      formatter: (row: any) => {
        // 计算反馈率：已反馈的报告数 / 总报告数
        const total = row.totalReports || 0
        const feedback = row.feedbackReports || 0
        if (total === 0) return '0%'
        return `${Math.round((feedback / total) * 100)}%`
      }
    },
    {
      minWidth: 160,
      label: '创建时间',
      prop: 'createTime',
      formatter: (row: any) => formatDate(row.createTime, 'YYYY-MM-DD HH:mm:ss')
    }
  ],
  dataList: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    refreshData: ({ page, size }: { page: number; size: number }) => {
      TableConfig.pagination.page = page
      TableConfig.pagination.limit = size
      refreshData()
    }
  },
  operations: [
    {
      perm: true,
      text: '查看反馈',
      isTextBtn: true,
      svgIcon: shallowRef(View),
      click: (row: any) => handleViewFeedback(row)
    },
    {
      perm: true,
      text: '反馈报告',
      isTextBtn: true,
      svgIcon: shallowRef(Document),
      click: (row: any) => handleViewReport(row)
    },
    {
      perm: true,
      text: '导出',
      isTextBtn: true,
      svgIcon: shallowRef(Download),
      click: (row: any) => handleExport(row)
    }
  ],
  titleRight: [
    {
      style: {
        justifyContent: 'flex-end'
      },
      items: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '批量导出',
              type: 'primary',
              click: () => handleBatchExport()
            }
          ]
        }
      ]
    }
  ]
})

const refSearch = ref<ISearchIns>()

// 弹窗相关
const dialogVisible = ref(false)
const selectedTaskId = ref('')

// 刷新数据
const refreshData = async () => {
  try {
    TableConfig.loading = true
    const queryParams = refSearch.value?.queryParams || {}

    // 处理日期范围参数
    let fromTime = ''
    let toTime = ''
    if (queryParams.completeTime && Array.isArray(queryParams.completeTime)) {
      fromTime = queryParams.completeTime[0]
      toTime = queryParams.completeTime[1]
    }

    const params = {
      page: TableConfig.pagination.page || 1,
      size: TableConfig.pagination.limit || 20,
      keyword: queryParams.keyword,
      receiveUserId: queryParams.receiveUserId,
      fromTime,
      toTime,
      // 只查询已完成、待审核、已驳回的任务
      isReceived: '1' // 已接收的任务
    }

    const res = await GetPatrolTaskList(params)
    if (res?.data?.code === 200) {
      const responseData = res.data.data || []
      // 过滤出已完成相关状态的任务
      const filteredData = responseData.filter((item: any) =>
        ['APPROVED', 'VERIFY', 'REJECTED'].includes(item.status)
      )

      TableConfig.dataList = filteredData
      TableConfig.pagination.total = filteredData.length
    } else {
      ElMessage.error(res.data?.message || '获取数据失败')
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取数据失败')
  } finally {
    TableConfig.loading = false
  }
}