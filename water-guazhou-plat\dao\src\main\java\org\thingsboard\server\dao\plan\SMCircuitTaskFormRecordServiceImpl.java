package org.thingsboard.server.dao.plan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.sql.smartManagement.plan.SMCircuitTaskFormRecordMapper;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskFormRecordPageRequest;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 巡检任务表单记录服务实现
 */
@Service
public class SMCircuitTaskFormRecordServiceImpl implements SMCircuitTaskFormRecordService {

    @Autowired
    private SMCircuitTaskFormRecordMapper mapper;

    @Override
    public IPage<SMCircuitTaskFormRecord> findAllConditional(SMCircuitTaskFormRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<SMCircuitTaskFormRecord> findByTaskId(String taskId) {
        return mapper.findByTaskId(taskId);
    }

    @Override
    public List<SMCircuitTaskFormRecord> findByTaskCode(String taskCode) {
        return mapper.findByTaskCode(taskCode);
    }

    @Override
    public SMCircuitTaskFormRecord save(SMCircuitTaskFormRecord entity) {
        if (entity.getId() == null || entity.getId().isEmpty()) {
            entity.setCreateTime(new Date());
            mapper.insert(entity);
        } else {
            mapper.updateById(entity);
        }
        return entity;
    }

    @Override
    public boolean update(SMCircuitTaskFormRecord entity) {
        return mapper.updateById(entity) > 0;
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public SMCircuitTaskFormRecord findById(String id) {
        return mapper.selectById(id);
    }

    @Override
    @Transactional
    public int generateFormRecords(String taskId, String taskCode, String inspectionConfigId, String formConfig, String tenantId) {
        if (formConfig == null || formConfig.trim().isEmpty()) {
            return 0;
        }

        try {
            JSONArray formItems = JSON.parseArray(formConfig);
            List<SMCircuitTaskFormRecord> records = new ArrayList<>();
            
            for (int i = 0; i < formItems.size(); i++) {
                JSONObject item = formItems.getJSONObject(i);
                
                SMCircuitTaskFormRecord record = new SMCircuitTaskFormRecord();
                // 生成UUID作为ID
                record.setId(java.util.UUID.randomUUID().toString().replace("-", ""));
                record.setTaskId(taskId);
                record.setTaskCode(taskCode);
                record.setInspectionConfigId(inspectionConfigId);
                record.setFormItemId(String.valueOf(i)); // 使用索引作为表单项ID
                record.setLocation(item.getString("location"));
                record.setPosition(item.getString("position"));
                record.setContent(item.getString("content"));
                record.setResult("NOT_CHECKED"); // 默认未检查
                record.setCreateTime(new Date());
                record.setTenantId(tenantId);
                
                records.add(record);
            }
            
            if (!records.isEmpty()) {
                // 使用循环插入，符合项目风格
                for (SMCircuitTaskFormRecord record : records) {
                    // 确保ID不为空
                    if (record.getId() == null || record.getId().isEmpty()) {
                        record.setId(java.util.UUID.randomUUID().toString().replace("-", ""));
                    }
                    mapper.insert(record);
                }
                return records.size();
            }
            
        } catch (Exception e) {
            throw new RuntimeException("解析表单配置失败: " + e.getMessage(), e);
        }
        
        return 0;
    }



    /**
     * 更新检验结果
     *
     * @param id 记录ID
     * @param result 检验结果
     * @param resultDescription 结果描述
     * @param attachments 附件
     * @param checkUserId 检验人员ID
     * @return 是否更新成功
     */
    @Override
    @Transactional
    public boolean updateCheckResult(String id, String result, String resultDescription, String attachments, String checkUserId) {
        SMCircuitTaskFormRecord record = new SMCircuitTaskFormRecord();
        record.setId(id);
        record.setResult(result);
        record.setResultDescription(resultDescription);
        record.setAttachments(attachments);
        record.setCheckUserId(checkUserId);
        record.setCheckTime(new Date());
        return mapper.updateById(record) > 0;
    }

    /**
     * 批量更新检验结果
     *
     * @param records 表单记录列表
     * @return 是否更新成功
     */
    @Override
    @Transactional
    public boolean batchUpdateCheckResult(List<SMCircuitTaskFormRecord> records) {
        for (SMCircuitTaskFormRecord record : records) {
            record.setCheckTime(new Date());
            mapper.updateById(record);
        }
        return true;
    }

    /**
     * 根据任务ID删除表单记录
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    public boolean deleteByTaskId(String taskId) {
        return mapper.deleteByTaskId(taskId) >= 0;
    }

    /**
     * 检查任务表单是否全部完成
     *
     * @param taskId 任务ID
     * @return 是否全部完成
     */
    public boolean isTaskFormCompleted(String taskId) {
        List<SMCircuitTaskFormRecord> records = findByTaskId(taskId);
        if (records.isEmpty()) {
            return true; // 没有表单记录则认为已完成
        }

        // 检查是否所有记录都已检查
        return records.stream().allMatch(record ->
            !"NOT_CHECKED".equals(record.getResult())
        );
    }
}
