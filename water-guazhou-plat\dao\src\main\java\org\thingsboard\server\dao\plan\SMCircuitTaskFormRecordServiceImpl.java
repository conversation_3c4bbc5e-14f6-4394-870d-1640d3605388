package org.thingsboard.server.dao.plan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.sql.smartManagement.plan.SMCircuitTaskFormRecordMapper;
import org.thingsboard.server.common.data.UUIDConverter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 巡检任务表单记录服务实现
 */
@Service
public class SMCircuitTaskFormRecordServiceImpl implements SMCircuitTaskFormRecordService {

    @Autowired
    private SMCircuitTaskFormRecordMapper mapper;

    @Override
    public List<SMCircuitTaskFormRecord> findByTaskId(String taskId) {
        return mapper.findByTaskId(taskId);
    }

    @Override
    public List<SMCircuitTaskFormRecord> findByTaskCode(String taskCode) {
        return mapper.findByTaskCode(taskCode);
    }

    @Override
    @Transactional
    public int generateFormRecords(String taskId, String taskCode, String inspectionConfigId, String formConfig, String tenantId) {
        if (formConfig == null || formConfig.trim().isEmpty()) {
            return 0;
        }

        try {
            JSONArray formItems = JSON.parseArray(formConfig);
            List<SMCircuitTaskFormRecord> records = new ArrayList<>();
            
            for (int i = 0; i < formItems.size(); i++) {
                JSONObject item = formItems.getJSONObject(i);
                
                SMCircuitTaskFormRecord record = new SMCircuitTaskFormRecord();
                record.setTaskId(taskId);
                record.setTaskCode(taskCode);
                record.setInspectionConfigId(inspectionConfigId);
                record.setFormItemId(String.valueOf(i)); // 使用索引作为表单项ID
                record.setLocation(item.getString("location"));
                record.setPosition(item.getString("position"));
                record.setContent(item.getString("content"));
                record.setResult("NOT_CHECKED"); // 默认未检查
                record.setCreateTime(new Date());
                record.setTenantId(tenantId);
                
                records.add(record);
            }
            
            if (!records.isEmpty()) {
                return mapper.batchInsert(records);
            }
            
        } catch (Exception e) {
            throw new RuntimeException("解析表单配置失败", e);
        }
        
        return 0;
    }

    @Override
    public boolean updateCheckResult(String id, String result, String resultDescription, String attachments, String checkUserId) {
        return mapper.updateCheckResult(id, result, resultDescription, attachments, checkUserId) > 0;
    }

    @Override
    @Transactional
    public boolean batchUpdateCheckResult(List<SMCircuitTaskFormRecord> records) {
        for (SMCircuitTaskFormRecord record : records) {
            record.setCheckTime(new Date());
            mapper.updateById(record);
        }
        return true;
    }

    @Override
    public boolean deleteByTaskId(String taskId) {
        return mapper.deleteByTaskId(taskId) >= 0;
    }

    @Override
    public boolean isTaskFormCompleted(String taskId) {
        List<SMCircuitTaskFormRecord> records = mapper.findByTaskId(taskId);
        if (records.isEmpty()) {
            return true; // 没有表单记录则认为已完成
        }

        // 检查是否所有记录都已检查
        return records.stream().allMatch(record ->
            !"NOT_CHECKED".equals(record.getResult())
        );
    }

    @Override
    public boolean isTaskFormCompleted(String taskId) {
        List<SMCircuitTaskFormRecord> records = findByTaskId(taskId);
        if (records.isEmpty()) {
            return true; // 没有表单项则认为已完成
        }
        
        for (SMCircuitTaskFormRecord record : records) {
            if ("NOT_CHECKED".equals(record.getResult())) {
                return false;
            }
        }
        return true;
    }
}
