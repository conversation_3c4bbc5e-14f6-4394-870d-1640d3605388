package org.thingsboard.server.dao.plan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecordResponse;
import org.thingsboard.server.dao.sql.smartManagement.plan.SMCircuitTaskFormRecordMapper;
import org.thingsboard.server.dao.util.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskFormRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskFormRecordSaveRequest;
import org.thingsboard.server.common.data.UUIDConverter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 巡检任务表单记录服务实现
 */
@Service
public class SMCircuitTaskFormRecordServiceImpl implements SMCircuitTaskFormRecordService {

    @Autowired
    private SMCircuitTaskFormRecordMapper mapper;

    @Override
    public IPage<SMCircuitTaskFormRecordResponse> findAllConditional(SMCircuitTaskFormRecordPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public List<SMCircuitTaskFormRecordResponse> findByTaskId(String taskId) {
        return mapper.findByTaskId(taskId);
    }

    @Override
    public List<SMCircuitTaskFormRecordResponse> findByTaskCode(String taskCode) {
        return mapper.findByTaskCode(taskCode);
    }

    @Override
    public SMCircuitTaskFormRecord save(SMCircuitTaskFormRecordSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public List<SMCircuitTaskFormRecord> saveAll(List<SMCircuitTaskFormRecordSaveRequest> entities) {
        return QueryUtil.saveOrUpdateBatchByRequest(entities,
            records -> mapper.batchInsert(records),
            records -> mapper.batchInsert(records));
    }

    @Override
    public boolean update(SMCircuitTaskFormRecord entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        return mapper.deleteBatchIds(idList) > 0;
    }

    @Override
    @Transactional
    public int generateFormRecords(String taskId, String taskCode, String inspectionConfigId, String formConfig, String tenantId) {
        if (formConfig == null || formConfig.trim().isEmpty()) {
            return 0;
        }

        try {
            JSONArray formItems = JSON.parseArray(formConfig);
            List<SMCircuitTaskFormRecord> records = new ArrayList<>();
            
            for (int i = 0; i < formItems.size(); i++) {
                JSONObject item = formItems.getJSONObject(i);
                
                SMCircuitTaskFormRecord record = new SMCircuitTaskFormRecord();
                record.setTaskId(taskId);
                record.setTaskCode(taskCode);
                record.setInspectionConfigId(inspectionConfigId);
                record.setFormItemId(String.valueOf(i)); // 使用索引作为表单项ID
                record.setLocation(item.getString("location"));
                record.setPosition(item.getString("position"));
                record.setContent(item.getString("content"));
                record.setResult("NOT_CHECKED"); // 默认未检查
                record.setCreateTime(new Date());
                record.setTenantId(tenantId);
                
                records.add(record);
            }
            
            if (!records.isEmpty()) {
                return mapper.batchInsert(records);
            }
            
        } catch (Exception e) {
            throw new RuntimeException("解析表单配置失败", e);
        }
        
        return 0;
    }

    @Override
    public boolean updateCheckResult(String id, String result, String resultDescription, String attachments, String checkUserId) {
        return mapper.updateCheckResult(id, result, resultDescription, attachments, checkUserId) > 0;
    }

    @Override
    @Transactional
    public boolean batchUpdateCheckResult(List<SMCircuitTaskFormRecord> records) {
        for (SMCircuitTaskFormRecord record : records) {
            record.setCheckTime(new Date());
            mapper.updateById(record);
        }
        return true;
    }

    @Override
    public boolean deleteByTaskId(String taskId) {
        return mapper.deleteByTaskId(taskId) >= 0;
    }

    /**
     * 生成表单记录
     *
     * @param taskId 任务ID
     * @param taskCode 任务编号
     * @param inspectionConfigId 巡检配置ID
     * @param formConfig 表单配置JSON
     * @param tenantId 租户ID
     * @return 生成的记录数量
     */
    @Transactional
    public int generateFormRecords(String taskId, String taskCode, String inspectionConfigId, String formConfig, String tenantId) {
        if (formConfig == null || formConfig.trim().isEmpty()) {
            return 0;
        }

        try {
            JSONArray formItems = JSON.parseArray(formConfig);
            List<SMCircuitTaskFormRecord> records = new ArrayList<>();

            for (int i = 0; i < formItems.size(); i++) {
                JSONObject item = formItems.getJSONObject(i);

                SMCircuitTaskFormRecord record = new SMCircuitTaskFormRecord();
                // 生成UUID作为ID
                record.setId(java.util.UUID.randomUUID().toString().replace("-", ""));
                record.setTaskId(taskId);
                record.setTaskCode(taskCode);
                record.setInspectionConfigId(inspectionConfigId);
                record.setFormItemId(String.valueOf(i)); // 使用索引作为表单项ID
                record.setLocation(item.getString("location"));
                record.setPosition(item.getString("position"));
                record.setContent(item.getString("content"));
                record.setResult("NOT_CHECKED"); // 默认未检查
                record.setCreateTime(new Date());
                record.setTenantId(tenantId);

                records.add(record);
            }

            if (!records.isEmpty()) {
                return mapper.batchInsert(records);
            }

        } catch (Exception e) {
            throw new RuntimeException("解析表单配置失败: " + e.getMessage(), e);
        }

        return 0;
    }

    /**
     * 更新检验结果
     *
     * @param id 记录ID
     * @param result 检验结果
     * @param resultDescription 结果描述
     * @param attachments 附件
     * @param checkUserId 检验人员ID
     * @return 是否更新成功
     */
    public boolean updateCheckResult(String id, String result, String resultDescription, String attachments, String checkUserId) {
        return mapper.updateCheckResult(id, result, resultDescription, attachments, checkUserId) > 0;
    }

    /**
     * 批量更新检验结果
     *
     * @param records 表单记录列表
     * @return 是否更新成功
     */
    @Transactional
    public boolean batchUpdateCheckResult(List<SMCircuitTaskFormRecord> records) {
        for (SMCircuitTaskFormRecord record : records) {
            record.setCheckTime(new Date());
            mapper.updateById(record);
        }
        return true;
    }

    /**
     * 根据任务ID删除表单记录
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    public boolean deleteByTaskId(String taskId) {
        return mapper.deleteByTaskId(taskId) >= 0;
    }

    /**
     * 检查任务表单是否全部完成
     *
     * @param taskId 任务ID
     * @return 是否全部完成
     */
    public boolean isTaskFormCompleted(String taskId) {
        return mapper.countUncompletedByTaskId(taskId) == 0;
    }
}
