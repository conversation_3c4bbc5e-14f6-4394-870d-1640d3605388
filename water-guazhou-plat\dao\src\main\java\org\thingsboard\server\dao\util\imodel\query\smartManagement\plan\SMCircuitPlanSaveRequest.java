package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import com.alibaba.fastjson.JSONArray;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlan;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class SMCircuitPlanSaveRequest extends SaveRequest<SMCircuitPlan> {
    // 计划名称
    @NotNullOrEmpty
    private String name;

    // 所属片区区域
    @NotNullOrEmpty
    private String districtAreaId;

    // 是否为常规计划
    @NotNullOrEmpty
    private Boolean isNormalPlan;

    // 是否需要反馈
    @NotNullOrEmpty
    private Boolean isNeedFeedback;

    // 行进方式：车巡、步行
    @NotNullOrEmpty
    private String moveType;

    // 设备
    private List<SMCircuitPlanDeviceSaveRequest> devices;

    // 专项设备
    private List<SMCircuitPlanDeviceSaveRequest> specialDevices;

    // 计划周期下标
    private Integer planCircle;

    // 备注
    private String remark;

    // 关联的巡检配置ID
    private String inspectionConfigId;

    public List<SMCircuitPlanDeviceSaveRequest> getDevices() {
        if (devices == null)
            return Collections.emptyList();
        return devices;
    }

    public List<SMCircuitPlanDeviceSaveRequest> getSpecialDevices() {
        if (specialDevices == null)
            return Collections.emptyList();
        return specialDevices;
    }

    @Override
    protected SMCircuitPlan build() {
        SMCircuitPlan entity = new SMCircuitPlan();
        entity.setCreator(currentUserUUID());
        entity.setCreateTime(new Date());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    protected SMCircuitPlan update(String id) {
        SMCircuitPlan entity = new SMCircuitPlan();
        entity.setId(id);
        commonSet(entity);
        return entity;
    }

    private void commonSet(SMCircuitPlan entity) {
        entity.setName(name);
        entity.setDistrictAreaId(districtAreaId);
        entity.setIsNormalPlan(isNormalPlan);
        entity.setIsNeedFeedback(isNeedFeedback);
        entity.setPlanCircle(planCircle);
        entity.setMoveType(moveType);
        entity.setDevices(JSONArray.toJSONString(devices));
        entity.setSpecialDevices(JSONArray.toJSONString(specialDevices));
        entity.setRemark(remark);
        entity.setInspectionConfigId(inspectionConfigId);
    }
}