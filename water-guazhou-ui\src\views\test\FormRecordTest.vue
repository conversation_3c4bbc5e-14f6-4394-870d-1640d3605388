<template>
  <div class="test-page">
    <h2>巡检任务表单记录测试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>测试API调用</span>
      </template>
      
      <el-form :model="testForm" label-width="120px">
        <el-form-item label="任务ID">
          <el-input v-model="testForm.taskId" placeholder="请输入任务ID"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="testGetFormRecords">获取表单记录</el-button>
          <el-button type="success" @click="testUpdateRecord">测试更新记录</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card>
      <template #header>
        <span>表单记录列表</span>
      </template>
      
      <el-table :data="formRecords" border>
        <el-table-column prop="id" label="ID" width="200"></el-table-column>
        <el-table-column prop="location" label="巡检地点"></el-table-column>
        <el-table-column prop="position" label="巡检位置"></el-table-column>
        <el-table-column prop="content" label="检验内容"></el-table-column>
        <el-table-column prop="result" label="检验结果">
          <template #default="{ row }">
            <el-tag :type="getResultType(row.result)">
              {{ getResultText(row.result) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resultDescription" label="结果描述"></el-table-column>
        <el-table-column prop="checkTime" label="检验时间"></el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="editRecord(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog v-model="dialogVisible" title="编辑表单记录" width="600px">
      <el-form :model="editForm" label-width="100px">
        <el-form-item label="巡检地点">
          <el-input v-model="editForm.location" readonly></el-input>
        </el-form-item>
        <el-form-item label="巡检位置">
          <el-input v-model="editForm.position" readonly></el-input>
        </el-form-item>
        <el-form-item label="检验内容">
          <el-input v-model="editForm.content" type="textarea" readonly></el-input>
        </el-form-item>
        <el-form-item label="检验结果">
          <el-radio-group v-model="editForm.result">
            <el-radio label="NORMAL">正常</el-radio>
            <el-radio label="ABNORMAL">异常</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="结果描述">
          <el-input v-model="editForm.resultDescription" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="附件">
          <el-input v-model="editForm.attachments" placeholder="附件路径，多个用逗号分隔"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveRecord">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getFormRecordsByTaskId, 
  updateCheckResult 
} from '@/api/smartManagement/circuitTaskFormRecord'

const testForm = reactive({
  taskId: ''
})

const formRecords = ref([])
const dialogVisible = ref(false)
const editForm = reactive({
  id: '',
  location: '',
  position: '',
  content: '',
  result: '',
  resultDescription: '',
  attachments: ''
})

// 获取表单记录
const testGetFormRecords = async () => {
  if (!testForm.taskId) {
    ElMessage.warning('请输入任务ID')
    return
  }
  
  try {
    const res = await getFormRecordsByTaskId(testForm.taskId)
    formRecords.value = res.data?.data || []
    ElMessage.success('获取成功')
  } catch (error) {
    console.error('获取失败:', error)
    ElMessage.error('获取失败')
  }
}

// 测试更新记录
const testUpdateRecord = async () => {
  if (formRecords.value.length === 0) {
    ElMessage.warning('请先获取表单记录')
    return
  }
  
  const firstRecord = formRecords.value[0]
  try {
    await updateCheckResult(firstRecord.id, {
      result: 'NORMAL',
      resultDescription: '测试更新',
      attachments: '',
      checkUserId: 'test_user'
    })
    ElMessage.success('更新成功')
    testGetFormRecords() // 重新获取数据
  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error('更新失败')
  }
}

// 编辑记录
const editRecord = (row: any) => {
  Object.assign(editForm, row)
  dialogVisible.value = true
}

// 保存记录
const saveRecord = async () => {
  try {
    await updateCheckResult(editForm.id, {
      result: editForm.result,
      resultDescription: editForm.resultDescription,
      attachments: editForm.attachments,
      checkUserId: 'current_user'
    })
    ElMessage.success('保存成功')
    dialogVisible.value = false
    testGetFormRecords() // 重新获取数据
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 获取结果类型
const getResultType = (result: string) => {
  const typeMap = {
    'NORMAL': 'success',
    'ABNORMAL': 'danger',
    'NOT_CHECKED': 'info'
  }
  return typeMap[result] || 'info'
}

// 获取结果文本
const getResultText = (result: string) => {
  const textMap = {
    'NORMAL': '正常',
    'ABNORMAL': '异常',
    'NOT_CHECKED': '未检查'
  }
  return textMap[result] || result
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
}
</style>
