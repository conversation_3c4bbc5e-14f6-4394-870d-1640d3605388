<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartManagement.plan.SMCircuitTaskFormRecordMapper">
    
    <sql id="Base_Column_List">
        id, task_id, task_code, inspection_config_id, form_item_id,
        location, position, content, result, result_description,
        attachments, check_time, check_user_id, create_time, tenant_id
    </sql>
    
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="task_code" property="taskCode"/>
        <result column="inspection_config_id" property="inspectionConfigId"/>
        <result column="form_item_id" property="formItemId"/>
        <result column="location" property="location"/>
        <result column="position" property="position"/>
        <result column="content" property="content"/>
        <result column="result" property="result"/>
        <result column="result_description" property="resultDescription"/>
        <result column="attachments" property="attachments"/>
        <result column="check_time" property="checkTime"/>
        <result column="check_user_id" property="checkUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <resultMap id="ResponseResultMap" type="org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecordResponse">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="task_code" property="taskCode"/>
        <result column="inspection_config_id" property="inspectionConfigId"/>
        <result column="form_item_id" property="formItemId"/>
        <result column="location" property="location"/>
        <result column="position" property="position"/>
        <result column="content" property="content"/>
        <result column="result" property="result"/>
        <result column="result_description" property="resultDescription"/>
        <result column="attachments" property="attachments"/>
        <result column="check_time" property="checkTime"/>
        <result column="check_user_id" property="checkUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 分页查询表单记录 -->
    <select id="findByPage" resultMap="ResponseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sm_circuit_task_form_record
        <where>
            <if test="taskId != null and taskId != ''">
                AND task_id = #{taskId}
            </if>
            <if test="taskCode != null and taskCode != ''">
                AND task_code = #{taskCode}
            </if>
            <if test="inspectionConfigId != null and inspectionConfigId != ''">
                AND inspection_config_id = #{inspectionConfigId}
            </if>
            <if test="result != null and result != ''">
                AND result = #{result}
            </if>
            <if test="location != null and location != ''">
                AND location LIKE CONCAT('%', #{location}, '%')
            </if>
            <if test="position != null and position != ''">
                AND position LIKE CONCAT('%', #{position}, '%')
            </if>
            <if test="checkUserId != null and checkUserId != ''">
                AND check_user_id = #{checkUserId}
            </if>
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
        </where>
        ORDER BY form_item_id
    </select>

    <select id="findByTaskId" resultMap="ResponseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sm_circuit_task_form_record
        WHERE task_id = #{taskId}
        ORDER BY form_item_id
    </select>

    <select id="findByTaskCode" resultMap="ResponseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sm_circuit_task_form_record
        WHERE task_code = #{taskCode}
        ORDER BY form_item_id
    </select>

    <!-- 更新实体 -->
    <update id="update">
        UPDATE sm_circuit_task_form_record
        <set>
            <if test="result != null">result = #{result},</if>
            <if test="resultDescription != null">result_description = #{resultDescription},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="checkUserId != null">check_user_id = #{checkUserId},</if>
        </set>
        WHERE id = #{id}
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sm_circuit_task_form_record (
            id, task_id, task_code, inspection_config_id, form_item_id,
            location, position, content, result, result_description,
            attachments, check_time, check_user_id, create_time, tenant_id
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.id}, #{record.taskId}, #{record.taskCode}, 
                #{record.inspectionConfigId}, #{record.formItemId},
                #{record.location}, #{record.position}, #{record.content}, 
                #{record.result}, #{record.resultDescription},
                #{record.attachments}, #{record.checkTime}, #{record.checkUserId}, 
                #{record.createTime}, #{record.tenantId}
            )
        </foreach>
    </insert>

    <update id="updateCheckResult">
        UPDATE sm_circuit_task_form_record
        SET result = #{result},
            result_description = #{resultDescription},
            attachments = #{attachments},
            check_user_id = #{checkUserId},
            check_time = NOW()
        WHERE id = #{id}
    </update>

    <delete id="deleteByTaskId">
        DELETE FROM sm_circuit_task_form_record
        WHERE task_id = #{taskId}
    </delete>

    <!-- 检查任务表单是否全部完成 -->
    <select id="countUncompletedByTaskId" resultType="int">
        SELECT COUNT(*)
        FROM sm_circuit_task_form_record
        WHERE task_id = #{taskId} AND result = 'NOT_CHECKED'
    </select>

</mapper>
