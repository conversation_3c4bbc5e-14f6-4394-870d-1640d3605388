package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.sql.smartManagement.district.CircuitDistrictMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class SMCircuitPlanResponse {
    // id
    @TableId
    private String id;

    // 计划名称
    private String name;

    // 所属片区
    @ParseViaMapper(CircuitDistrictMapper.class)
    private String topDistrictId;

    // 所属片区区域
    private String districtAreaId;

    // 所属片区区域
    private String districtAreaName;

    // 是否为常规计划
    private Boolean isNormalPlan;

    // 是否需要反馈
    private Boolean isNeedFeedback;

    // 行进方式：车巡、步行
    private String moveType;

    // 设备Id，多个用逗号隔开
    private String devices;

    // 专项设备Id，多个用逗号隔开
    private String specialDevices;

    // 计划周期
    @Info(name = "planCircleName")
    private String planCircle;

    // 备注
    private String remark;

    // 关联的巡检配置ID
    private String inspectionConfigId;

    // 创建人Id
    @ParseUsername
    private String creator;

    // 创建时间
    private Date createTime;

    // 租户Id
    private String tenantId;

    private String planCircleName() {
        return SMPlanCircle.indexOf(Integer.valueOf(planCircle)).getName();
    }

}
