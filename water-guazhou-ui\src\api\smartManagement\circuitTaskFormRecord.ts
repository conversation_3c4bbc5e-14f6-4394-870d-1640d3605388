// 巡检任务表单记录API
import request from '@/plugins/axios';

// 根据任务ID查询表单记录
export function getFormRecordsByTaskId(taskId: string) {
  return request({
    url: `/api/sm/circuitTaskFormRecord/task/${taskId}`,
    method: 'get'
  });
}

// 根据任务编号查询表单记录
export function getFormRecordsByTaskCode(taskCode: string) {
  return request({
    url: `/api/sm/circuitTaskFormRecord/taskCode/${taskCode}`,
    method: 'get'
  });
}

// 分页查询表单记录
export function getFormRecordsByPage(params: any) {
  return request({
    url: '/api/sm/circuitTaskFormRecord',
    method: 'get',
    params
  });
}

// 根据ID查询表单记录
export function getFormRecordById(id: string) {
  return request({
    url: `/api/sm/circuitTaskFormRecord/${id}`,
    method: 'get'
  });
}

// 保存表单记录
export function saveFormRecord(data: any) {
  return request({
    url: '/api/sm/circuitTaskFormRecord',
    method: 'post',
    data
  });
}

// 更新表单记录
export function updateFormRecord(id: string, data: any) {
  return request({
    url: `/api/sm/circuitTaskFormRecord/${id}`,
    method: 'post',
    data
  });
}

// 删除表单记录
export function deleteFormRecord(id: string) {
  return request({
    url: `/api/sm/circuitTaskFormRecord/${id}`,
    method: 'delete'
  });
}

// 更新检验结果
export function updateCheckResult(id: string, data: {
  result: string;
  resultDescription?: string;
  attachments?: string;
  checkUserId?: string;
}) {
  return request({
    url: `/api/sm/circuitTaskFormRecord/${id}/checkResult`,
    method: 'post',
    data
  });
}

// 批量更新检验结果
export function batchUpdateCheckResult(records: any[]) {
  return request({
    url: '/api/sm/circuitTaskFormRecord/batchUpdate',
    method: 'post',
    data: records
  });
}

// 检查任务表单是否全部完成
export function isTaskFormCompleted(taskId: string) {
  return request({
    url: `/api/sm/circuitTaskFormRecord/task/${taskId}/completed`,
    method: 'get'
  });
}
