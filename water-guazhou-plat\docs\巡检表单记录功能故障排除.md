# 巡检表单记录功能故障排除指南

## 常见错误及解决方案

### 1. 事务回滚错误

**错误信息：**
```
TransactionSystemException: Could not commit JPA transaction; 
nested exception is javax.persistence.RollbackException: Transaction marked as rollbackOnly
```

**可能原因：**
- 数据库约束违反（如ID为null）
- 外键约束问题
- 数据类型不匹配

**解决方案：**
1. 检查实体类ID注解：
   ```java
   @TableId(type = IdType.INPUT)
   private String id;
   ```

2. 确保ID字段正确设置：
   ```java
   record.setId(java.util.UUID.randomUUID().toString().replace("-", ""));
   ```

3. 检查数据库表结构是否正确

### 2. ID字段为null错误

**错误信息：**
```
PSQLException: ERROR: null value in column "id" violates not-null constraint
```

**解决方案：**
1. 确保实体类使用正确的ID类型：
   ```java
   @TableId(type = IdType.INPUT)
   private String id;
   ```

2. 在插入前确保ID已设置：
   ```java
   if (record.getId() == null || record.getId().isEmpty()) {
       record.setId(java.util.UUID.randomUUID().toString().replace("-", ""));
   }
   ```

### 3. 表单配置解析失败

**错误信息：**
```
RuntimeException: 解析表单配置失败
```

**可能原因：**
- JSON格式错误
- 字段名称不匹配

**解决方案：**
1. 验证JSON格式：
   ```json
   [
     {
       "location": "厂内生活区A区",
       "position": "雨调蓄井",
       "content": "井身是否有裂纹、沉降现象，井盖是否完好"
     }
   ]
   ```

2. 检查字段名称是否正确（location, position, content）

### 4. 巡检配置未找到

**问题：** 选择了巡检配置但没有生成表单记录

**解决方案：**
1. 检查巡检配置是否存在：
   ```sql
   SELECT * FROM sm_circuit_settings WHERE id = 'your_config_id';
   ```

2. 检查巡检配置的状态是否为启用：
   ```sql
   SELECT * FROM sm_circuit_settings WHERE status = '0';
   ```

3. 检查表单配置字段是否有内容：
   ```sql
   SELECT id, name, form_config FROM sm_circuit_settings WHERE form_config IS NOT NULL;
   ```

## 调试步骤

### 1. 检查数据库表结构

```sql
-- 检查表是否存在
\dt sm_circuit_task_form_record

-- 检查表结构
\d sm_circuit_task_form_record

-- 检查约束
SELECT conname, contype FROM pg_constraint WHERE conrelid = 'sm_circuit_task_form_record'::regclass;
```

### 2. 检查巡检配置数据

```sql
-- 查看所有巡检配置
SELECT id, name, code, status, LENGTH(form_config) as config_length 
FROM sm_circuit_settings;

-- 查看具体的表单配置
SELECT id, name, form_config 
FROM sm_circuit_settings 
WHERE id = 'your_config_id';
```

### 3. 检查巡检计划关联

```sql
-- 查看巡检计划是否正确关联配置
SELECT id, name, inspection_config_id 
FROM sm_circuit_plan 
WHERE inspection_config_id IS NOT NULL;
```

### 4. 检查表单记录生成

```sql
-- 查看生成的表单记录
SELECT task_id, inspection_config_id, location, position, content 
FROM sm_circuit_task_form_record 
ORDER BY task_id, form_item_id;

-- 统计表单记录数量
SELECT task_id, COUNT(*) as record_count 
FROM sm_circuit_task_form_record 
GROUP BY task_id;
```

## 测试验证

### 1. 单元测试

创建简单的测试用例验证表单记录生成：

```java
@Test
public void testGenerateFormRecords() {
    String taskId = "test_task_001";
    String taskCode = "TEST_001";
    String configId = "test_config_001";
    String formConfig = "[{\"location\":\"测试地点\",\"position\":\"测试位置\",\"content\":\"测试内容\"}]";
    String tenantId = "test_tenant";
    
    int count = formRecordService.generateFormRecords(taskId, taskCode, configId, formConfig, tenantId);
    
    assertEquals(1, count);
}
```

### 2. 集成测试

1. 创建测试用的巡检配置
2. 创建巡检计划并关联配置
3. 生成巡检任务
4. 验证表单记录是否正确生成

### 3. 前端测试

使用测试页面 `/test/InspectionFormTest` 进行功能验证：

1. 测试巡检配置的加载
2. 测试计划配置绑定
3. 测试表单记录生成

## 性能优化

### 1. 批量插入优化

如果表单项较多，考虑使用批量插入：

```java
// 使用事务批量处理
@Transactional
public int batchGenerateFormRecords(List<TaskFormData> taskFormDataList) {
    int totalCount = 0;
    for (TaskFormData data : taskFormDataList) {
        totalCount += generateFormRecords(data.getTaskId(), data.getTaskCode(), 
                                        data.getConfigId(), data.getFormConfig(), data.getTenantId());
    }
    return totalCount;
}
```

### 2. 缓存优化

对于频繁访问的巡检配置，考虑添加缓存：

```java
@Cacheable(value = "circuitSettings", key = "#id")
public CircuitSettings findById(String id) {
    return circuitSettingsMapper.selectById(id);
}
```

## 监控和日志

### 1. 添加业务日志

```java
log.info("开始为任务 {} 生成表单记录，配置ID: {}", taskCode, inspectionConfigId);
log.info("成功生成 {} 条表单记录", recordCount);
```

### 2. 异常监控

```java
try {
    // 表单记录生成逻辑
} catch (Exception e) {
    log.error("生成表单记录失败，任务ID: {}, 配置ID: {}, 错误: {}", 
              taskId, inspectionConfigId, e.getMessage(), e);
    // 发送告警通知
}
```

## 数据备份和恢复

### 1. 备份表单记录

```sql
-- 备份表单记录数据
COPY sm_circuit_task_form_record TO '/path/to/backup/form_records.csv' WITH CSV HEADER;
```

### 2. 恢复表单记录

```sql
-- 恢复表单记录数据
COPY sm_circuit_task_form_record FROM '/path/to/backup/form_records.csv' WITH CSV HEADER;
```

通过以上故障排除指南，可以快速定位和解决巡检表单记录功能中的常见问题。
