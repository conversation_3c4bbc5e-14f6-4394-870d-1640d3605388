<!-- 计划制定 -->
<template>
  <RightDrawerMap
    ref="refMap"
    title="计划制定"
    :right-drawer-width="500"
    @map-loaded="onMaploaded"
  >
    <Form
      ref="refForm"
      :config="FormConfig"
    ></Form>
  </RightDrawerMap>
</template>
<script lang="ts" setup>
import Graphic from '@arcgis/core/Graphic'
import Point from '@arcgis/core/geometry/Point'
import {
  createGeometry,
  excuteQuery,
  getGraphicLayer,
  getPipeOptions,
  gotoAndHighLight,
  initQueryParams,
  setSymbol
} from '@/utils/MapHelper'
import RightDrawerMap from '../../components/common/RightDrawerMap.vue'
import { AddPatrolPlan, GetAreaTree, GetDistrictPointsJson, GetKeyPoint, GetPatrolPeriodPlanList } from '@/api/patrol'
import { formatTree } from '@/utils/GlobalHelper'
import { SLMessage } from '@/utils/Message'
import { getCircuitSettingsList } from '@/api/CircuitSettings/circuitSettings'

const refForm = ref<IFormIns>()
const refMap = ref<InstanceType<typeof RightDrawerMap>>()
const staticState: {
  view?: __esri.MapView
  /** 用于查询的要素 */
  graphic?: __esri.Graphic
  /** 区域信息和关键信息图层 */
  graphicsLayer?: __esri.GraphicsLayer
  deviceLayer?: __esri.GraphicsLayer
  deviceGraphics: __esri.Graphic[]
} = {
  deviceGraphics: []
}
const state = reactive<{
  tabs: any[]
  loading: boolean
  districtOptions: NormalOption[]
  layerOptions: NormalOption[]
  planPeriod: NormalOption[]
  circuitSettingsOptions: NormalOption[]
}>({
  tabs: [],
  loading: false,
  districtOptions: [],
  layerOptions: [],
  planPeriod: [],
  circuitSettingsOptions: []
})
const FormConfig = reactive<IFormConfig>({
  labelWidth: 120,
  group: [
    {
      fields: [
        {
          type: 'select-tree',
          field: 'districtAreaId',
          label: '区域',
          defaultExpandAll: true,
          options: computed(() => state.districtOptions) as any,
          rules: [{ required: true, message: '请选择区域' }],
          nodeClick: data => handleDistrictClick(data)
        },
        {
          type: 'select',
          label: '设备类别',
          field: 'devices',
          multiple: true,
          options: computed(() => state.layerOptions.filter(item => item.data.geometrytype !== 'esriGeometryPolyline')) as any,
          onChange: () => refreshDevice()
          // rules: [{ required: true, message: '请选择设备类别' }]
        },
        {
          type: 'radio',
          label: '是否常规计划',
          field: 'isNormalPlan',
          rules: [{ required: true, message: '请选择是否常规计划' }],
          options: [
            { label: '常规', value: 'true' },
            { label: '临时', value: 'false' }
          ]
        },
        {
          type: 'radio',
          label: '是否需要反馈',
          field: 'isNeedFeedback',
          rules: [{ required: true, message: '请选择是否需要反馈' }],
          options: [
            { label: '需要', value: 'true' },
            { label: '仅到位', value: 'false' }
          ]
        },
        {
          type: 'radio',
          label: '进行方式',
          field: 'moveType',
          rules: [{ required: true, message: '请选择进行方式' }],
          options: [
            { label: '车巡', value: '车巡' },
            { label: '步行', value: '步行' }
          ]
        },
        {
          type: 'select',
          label: '计划周期',
          field: 'planCircle',
          options: computed(() => state.planPeriod) as any,
          rules: [{ required: true, message: '请选择计划周期' }]
        },
        {
          type: 'input',
          label: '计划名称',
          field: 'name',
          rules: [{ required: true, message: '请输入计划名称' }]
        },
        {
          type: 'select',
          label: '巡检配置',
          field: 'inspectionConfigId',
          placeholder: '请选择巡检配置（可选）',
          options: computed(() => state.circuitSettingsOptions) as any,
          clearable: true
        },
        {
          type: 'textarea',
          label: '备注',
          field: 'remark'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              text: '重置',
              type: 'default',
              styles: {
                marginLeft: 'auto'
              },
              click: () => {
                refForm.value?.resetForm()
                staticState.graphicsLayer?.removeAll()
                staticState.deviceLayer?.removeAll()
              }
            },
            { perm: true, text: '确定', click: () => refForm.value?.Submit() }
          ]
        }
      ]
    }
  ],
  labelPosition: 'right',
  gutter: 12,
  defaultValue: {
    isNormalPlan: 'true',
    isNeedFeedback: 'false',
    moveType: '车巡',
    planCircle: '',
    devices: [],
    inspectionConfigId: ''
  },
  submit: params => {
    const deviceOids = staticState.deviceGraphics.map(item => item.attributes)
    const submitParams = {
      ...params,
      specialDevices: [],
      devices: deviceOids
    }
    AddPatrolPlan(submitParams)
      .then(res => {
        if (res.data.code === 200) {
          SLMessage.success(res.data.message)
          refForm.value?.resetForm()
          staticState.graphicsLayer?.removeAll()
          staticState.deviceLayer?.removeAll()
          staticState.graphic = undefined
          staticState.deviceGraphics = []
        } else {
          SLMessage.error(res.data.message)
        }
      })
      .catch(message => {
        SLMessage.error(message || '系统错误')
      })
  }
})
const handleDistrictClick = (data: NormalOption) => {
  /** 只对layer===2的节点进行处理 */
  if (data.data.layer !== 2) {
    return
  }
  staticState.graphicsLayer?.removeAll()
  staticState.deviceLayer?.removeAll()
  GetDistrictPointsJson(data.value).then(res => {
    const type = data.data.type === '区域' ? 'polygon' : 'polyline'
    if (!res.data.data) return
    const pointjson = JSON.parse(res.data.data)

    if (pointjson.geometry) {
      const geo = createGeometry(
        type,
        type === 'polygon' ? pointjson.geometry.rings : pointjson.geometry.paths,
        pointjson.geometry.spatialReference
      )
      staticState.graphic = new Graphic({
        geometry: geo,
        symbol: setSymbol(type)
      }) as __esri.Graphic
      staticState.graphicsLayer?.add(staticState.graphic)
      gotoAndHighLight(staticState.view, staticState.graphic, {
        avoidHighlight: true
      })
    }
    if (pointjson.bufferGeometry) {
      const bufferGeometry = createGeometry(
        'polygon',
        pointjson.bufferGeometry.rings,
        pointjson.bufferGeometry.spatialReference
      )
      const graphic = new Graphic({
        geometry: bufferGeometry,
        symbol: setSymbol(bufferGeometry?.type || 'polygon', {
          color: [0, 255, 0, 0.1],
          outlineWidth: 1,
          outlineColor: '#00ff00'
        })
      })
      staticState.graphicsLayer?.add(graphic)
      staticState.graphic = graphic
    }
    // 获取关键点列表
  })
  refreshKeyPoint(data.value)
}
const refreshKeyPoint = (areaId: string) => {
  GetKeyPoint({ areaId })
    .then(res => {
      const data = res.data.data
      data?.data.map(item => {
        const geometry = new Point({
          longitude: item.lon,
          latitude: item.lat,
          spatialReference: staticState.view?.spatialReference
        })
        const g = new Graphic({
          geometry,
          symbol: setSymbol('point', {
            outlineWidth: 1,
            outlineColor: '#00ffff',
            color: '#ffffff'
          })
        })
        const t = new Graphic({
          geometry,
          symbol: setSymbol('text', {
            text: item.name
          })
        })
        staticState.graphicsLayer?.addMany([g, t])
      })
    })
    .catch((error: any) => {
      console.log(error.message)
    })
}
const refreshDevice = () => {
  if (!staticState.graphic) {
    SLMessage.warning('请先选择区域')
    refForm.value && (refForm.value.dataForm.devices = [])
    return
  }
  staticState.deviceLayer?.removeAll()
  staticState.deviceGraphics = []
  const layerIds = refForm.value?.dataForm.devices
  layerIds.map(item => {
    excuteQuery(
      window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService + '/' + item,
      initQueryParams({
        where: '1=1',
        geometry: staticState.graphic?.geometry
      })
    ).then(res => {
      staticState.deviceGraphics.push(...res.features)
      res.features.map(o => {
        o.symbol = setSymbol(o.geometry.type, {
          color: [255, 0, 0, 0.3],
          outlineColor: [255, 0, 0, 1],
          outlineWidth: 1,
          size: 15
        })
        o.attributes = {
          serialId: o.attributes.OBJECTID,
          name: state.layerOptions.find(l => l.value === item)?.label
        }
      })
      staticState.deviceLayer?.addMany(res.features)
    })
  })
}
const getLayerInfo = async () => {
  state.layerOptions = await getPipeOptions(staticState.view)
}
const getDistrictOptions = () => {
  GetAreaTree().then(res => {
    const node = res.data.data
    state.districtOptions = formatTree([node])
  })
}
const getPlanPeriod = () => {
  GetPatrolPeriodPlanList().then(res => {
    FormConfig.defaultValue && (FormConfig.defaultValue.planCircle = 0)
    refForm.value?.resetForm()
    state.planPeriod = res.data.data.map((item, i) => {
      return {
        label: item,
        value: i,
        id: i
      }
    })
  })
}

const getCircuitSettings = async () => {
  try {
    const res = await getCircuitSettingsList({
      page: 1,
      size: 100,
      status: '0' // 只获取启用状态的配置
    })
    if (res.data?.data?.data) {
      state.circuitSettingsOptions = res.data.data.data.map((item: any) => ({
        label: `${item.name} (${item.code})`,
        value: item.id,
        id: item.id,
        data: item
      }))
    }
  } catch (error) {
    console.error('获取巡检配置失败:', error)
    SLMessage.error('获取巡检配置失败')
  }
}
const onMaploaded = async view => {
  staticState.view = view
  staticState.graphicsLayer = getGraphicLayer(staticState.view, {
    id: 'area-keypoints',
    title: '区域信息'
  })
  staticState.deviceLayer = getGraphicLayer(staticState.view, {
    id: 'device-points',
    title: '设备'
  })
  await getLayerInfo()
}
onBeforeMount(() => {
  getPlanPeriod()
  getDistrictOptions()
  getCircuitSettings()
})
</script>
<style lang="scss" scoped></style>
