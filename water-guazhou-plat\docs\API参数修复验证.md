# API参数修复验证文档

## 问题描述

巡检反馈详情弹窗中，传递给API的参数不正确：
- **问题**：传递的是任务ID (`task.id`)
- **需要**：传递的是任务编号 (`task.code`)

## 修复内容

### 1. 主页面参数传递修改

#### 修复前
```javascript
// 弹窗相关
const dialogVisible = ref(false)
const selectedTaskId = ref('')

// 查看反馈详情
const handleViewFeedback = (row: any) => {
  selectedTaskId.value = row.id  // 只传递ID
  dialogVisible.value = true
}

// 弹窗组件
<FeedbackDetailDialog
  v-model="dialogVisible"
  :task-id="selectedTaskId"  // 只传递ID
  @success="handleDialogSuccess"
/>
```

#### 修复后
```javascript
// 弹窗相关
const dialogVisible = ref(false)
const selectedTask = ref<any>({})  // 传递完整任务对象

// 查看反馈详情
const handleViewFeedback = (row: any) => {
  selectedTask.value = row  // 传递完整任务对象
  dialogVisible.value = true
}

// 弹窗组件
<FeedbackDetailDialog
  v-model="dialogVisible"
  :task="selectedTask"  // 传递完整任务对象
  @success="handleDialogSuccess"
/>
```

### 2. 弹窗组件Props修改

#### 修复前
```typescript
interface Props {
  modelValue: boolean
  taskId: string  // 只接收ID
}

const props = defineProps<Props>()
```

#### 修复后
```typescript
interface Props {
  modelValue: boolean
  task: any  // 接收完整任务对象
}

const props = defineProps<Props>()
```

### 3. API调用修改

#### 修复前
```javascript
import { getFormRecordsByTaskId } from '@/api/smartManagement/circuitTaskFormRecord'

const loadFormRecords = async () => {
  if (!props.taskId) return
  
  const res = await getFormRecordsByTaskId(props.taskId)  // 使用ID调用
  // ...
}
```

#### 修复后
```javascript
import { getFormRecordsByTaskCode } from '@/api/smartManagement/circuitTaskFormRecord'

const loadFormRecords = async () => {
  if (!props.task?.code) return
  
  const res = await getFormRecordsByTaskCode(props.task.code)  // 使用code调用
  // ...
}
```

### 4. 任务信息显示修改

#### 修复前
```vue
<div class="task-info">
  <p><strong>任务ID:</strong> {{ taskId }}</p>
  <p><strong>状态:</strong> 加载中...</p>
</div>
```

#### 修复后
```vue
<div class="task-info">
  <p><strong>任务编号:</strong> {{ task.code }}</p>
  <p><strong>任务名称:</strong> {{ task.name }}</p>
  <p><strong>执行人员:</strong> {{ task.receiveUserName }}</p>
  <p><strong>任务状态:</strong> {{ getStatusText(task.status) }}</p>
</div>
```

## API接口对比

### 可用的API接口
```javascript
// 1. 根据任务ID查询（原来使用的）
export function getFormRecordsByTaskId(taskId: string) {
  return request({
    url: `/api/sm/circuitTaskFormRecord/task/${taskId}`,
    method: 'get'
  });
}

// 2. 根据任务编号查询（现在使用的）
export function getFormRecordsByTaskCode(taskCode: string) {
  return request({
    url: `/api/sm/circuitTaskFormRecord/taskCode/${taskCode}`,
    method: 'get'
  });
}
```

### 后端接口实现
```java
// 根据任务ID查询
@GetMapping("/task/{taskId}")
public List<SMCircuitTaskFormRecord> getByTaskId(@PathVariable String taskId) {
    return service.findByTaskId(taskId);
}

// 根据任务编号查询
@GetMapping("/taskCode/{taskCode}")
public List<SMCircuitTaskFormRecord> getByTaskCode(@PathVariable String taskCode) {
    return service.findByTaskCode(taskCode);
}
```

## 数据流对比

### 修复前的数据流
```
任务列表 → 点击查看反馈 → 传递task.id → API调用错误
```

### 修复后的数据流
```
任务列表 → 点击查看反馈 → 传递完整task对象 → 使用task.code调用API → 正确获取数据
```

## 验证步骤

### 1. 检查任务数据结构
```javascript
// 在任务列表中打印任务数据
console.log('任务数据:', row)
// 应该包含：
// {
//   id: "task_id",
//   code: "202506090008",
//   name: "任务名称",
//   receiveUserName: "执行人员",
//   status: "APPROVED",
//   // ... 其他字段
// }
```

### 2. 检查弹窗接收的数据
```javascript
// 在弹窗组件中打印接收的数据
watch(visible, (newVal) => {
  if (newVal) {
    console.log('弹窗接收的任务数据:', props.task)
    console.log('任务编号:', props.task?.code)
  }
})
```

### 3. 检查API调用
```javascript
// 在API调用前打印参数
const loadFormRecords = async () => {
  console.log('API调用参数 - taskCode:', props.task.code)
  
  const res = await getFormRecordsByTaskCode(props.task.code)
  console.log('API响应:', res.data)
}
```

### 4. 检查网络请求
在浏览器开发者工具的Network面板中查看：
- **修复前**：`/api/sm/circuitTaskFormRecord/task/{taskId}`
- **修复后**：`/api/sm/circuitTaskFormRecord/taskCode/{taskCode}`

## 测试用例

### 测试用例1：正常任务
```javascript
// 输入数据
const task = {
  id: "abc123",
  code: "202506090008",
  name: "水厂日常巡检",
  status: "APPROVED"
}

// 预期结果
// API调用：GET /api/sm/circuitTaskFormRecord/taskCode/202506090008
// 返回：表单记录列表
```

### 测试用例2：无编号任务
```javascript
// 输入数据
const task = {
  id: "abc123",
  code: null,
  name: "测试任务"
}

// 预期结果
// 不调用API，显示提示信息
```

### 测试用例3：空任务对象
```javascript
// 输入数据
const task = null

// 预期结果
// 不调用API，显示空状态
```

## 错误处理

### 1. 任务编号为空
```javascript
const loadFormRecords = async () => {
  if (!props.task?.code) {
    ElMessage.warning('任务编号为空，无法加载表单记录')
    return
  }
  // ... API调用
}
```

### 2. API调用失败
```javascript
try {
  const res = await getFormRecordsByTaskCode(props.task.code)
  // 处理响应
} catch (error) {
  console.error('加载表单记录失败:', error)
  ElMessage.error('加载表单记录失败')
  FormRecordTableConfig.dataList = []
}
```

## 兼容性考虑

### 后端兼容性
确保后端同时支持两种查询方式：
- 按任务ID查询：适用于内部系统调用
- 按任务编号查询：适用于前端界面显示

### 前端兼容性
保留原有的API接口，以防其他地方还在使用：
```javascript
// 保留原有接口
export function getFormRecordsByTaskId(taskId: string) { ... }

// 新增接口
export function getFormRecordsByTaskCode(taskCode: string) { ... }
```

## 总结

通过以上修复：

1. **参数传递正确**：从传递ID改为传递完整任务对象
2. **API调用正确**：使用任务编号而不是任务ID
3. **数据显示完整**：显示任务的详细信息
4. **错误处理完善**：处理各种异常情况

修复后的功能应该能够正确加载和显示巡检表单记录。
