-- 测试用巡检配置数据
-- 确保表单配置JSON格式正确

-- 清理测试数据
DELETE FROM sm_circuit_task_form_record WHERE inspection_config_id IN ('test_config_001', 'test_config_002');
DELETE FROM sm_circuit_settings WHERE id IN ('test_config_001', 'test_config_002');

-- 确保数据库表存在
-- 如果表不存在，请先运行 circuit_task_form_record.sql 脚本

-- 插入测试巡检配置
INSERT INTO sm_circuit_settings (id, name, code, type, status, form_config, tenant_id, create_time) VALUES
(
  'test_config_001', 
  '水厂巡检配置测试', 
  'WATER_PLANT_TEST_001', 
  '1', 
  '0', 
  '[
    {
      "location": "厂内生活区A区",
      "position": "雨调蓄井",
      "content": "井身是否有裂纹、沉降现象，井盖是否完好"
    },
    {
      "location": "厂内生活区A区",
      "position": "雨调蓄井",
      "content": "井内闸阀、管道连接位置是否有渗水现象，井内是否有积水"
    },
    {
      "location": "厂内生活区A区",
      "position": "雨调蓄井",
      "content": "闸门、井内设备是否有丢失现象"
    },
    {
      "location": "厂内生活区A区",
      "position": "引供水管线",
      "content": "压力、流量计设备是否完好，工作是否正常"
    },
    {
      "location": "厂内生活区A区",
      "position": "引供水管线",
      "content": "现场采取的维护内容、措施"
    }
  ]', 
  'default_tenant',
  CURRENT_TIMESTAMP
),
(
  'test_config_002', 
  '管网巡检配置测试', 
  'PIPELINE_TEST_001', 
  '0', 
  '0', 
  '[
    {
      "location": "主干管网",
      "position": "阀门井",
      "content": "阀门开关是否正常，有无漏水现象"
    },
    {
      "location": "主干管网",
      "position": "检查井",
      "content": "井盖是否完好，井内是否有异物"
    },
    {
      "location": "支管网络",
      "position": "用户接入点",
      "content": "水表运行是否正常，管道连接是否牢固"
    }
  ]', 
  'default_tenant',
  CURRENT_TIMESTAMP
);

-- 验证插入的数据
SELECT 
  id, 
  name, 
  code, 
  type, 
  status,
  LENGTH(form_config) as form_config_length,
  create_time
FROM sm_circuit_settings 
WHERE id IN ('test_config_001', 'test_config_002');

-- 验证JSON格式是否正确
SELECT 
  id,
  name,
  JSON_ARRAY_LENGTH(form_config::json) as form_items_count
FROM sm_circuit_settings 
WHERE id IN ('test_config_001', 'test_config_002');
