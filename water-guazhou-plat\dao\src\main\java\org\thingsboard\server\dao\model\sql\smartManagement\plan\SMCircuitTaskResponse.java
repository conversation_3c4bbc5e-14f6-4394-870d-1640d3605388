package org.thingsboard.server.dao.model.sql.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.sql.user.UserMapper;
import org.thingsboard.server.dao.util.imodel.response.annotations.*;

import java.util.Date;

@Getter
@Setter
@ResponseEntity
public class SMCircuitTaskResponse {
    // id
    private String id;

    // 任务编号
    private String code;

    // 所属巡检计划Id
    private String planId;

    // 所属片区区域、路线(点阵)Id
    private String districtAreaId;

    // 所属片区区域、路线(点阵)名称
    private String districtAreaName;

    // 是否为常规计划
    private Boolean isNormalPlan;

    // 是否需要反馈
    private Boolean isNeedFeedback;

    // 行进方式：车巡、步行
    private String moveType;

    // 设备Id，多个用逗号隔开
    private String devices;

    // 专项设备Id，多个用逗号隔开
    private String specialDevices;

    // 任务名称
    private String name;

    // 接收人员Id
    @ParseUsername(withDepartment = true)
    private String receiveUserId;

    // 共同完成人Id，多个用逗号隔开
    @ParseViaMapper(value = UserMapper.class, method = "getNameByMultiId")
    private String collaborateUserId;

    // 到位距离
    private String presentDistance;

    // 描述
    private String remark;

    // 到位状况
    private String presentState;

    // 任务状态
    private GeneralTaskStatus status;

    // 反馈状况
    private String fallbackState;

    // 关键点数量
    private Integer keyPointCount;

    // 设备数量
    private Integer deviceCount;

    // 创建人Id
    @ParseUsername(withDepartment = true)
    private String creator;

    // 创建时间
    private Date createTime;

    // 开始时间
    private Date beginTime;

    // 结束时间
    private Date endTime;

    // 租户Id
    private String tenantId;

    // 审核人
    private String auditUserId;

    //审核时间
    private Date auditTime;

    //拒绝原因
    private String rejectReason;

    // 计划周期
    @Info(name = "planCircleName")
    private String planCircle;

    // 表单完成率（百分比）
    private Integer formCompletionRate;

    // 总表单记录数
    private Integer totalFormRecords;

    // 已完成表单记录数
    private Integer completedFormRecords;

    @SuppressWarnings("unused")
    private String planCircleName() {
        if (planCircle == null) {
            return "";
        } else {
            return SMPlanCircle.indexOf(Integer.valueOf(planCircle)).getName();
        }
    }
}
