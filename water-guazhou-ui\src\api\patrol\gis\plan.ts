import { request } from '@/plugins/axios';

/**
 * 批量删除巡检计划
 * @param params
 * @returns
 */
export const DeletePatrolPlans = (ids: string[]) => {
  return request({
    url: '/api/sm/circuitPlan',
    method: 'delete',
    data: ids
  });
};
/**
 * 查询巡检计划
 * @param params
 * @returns
 */
export const GetPatrolPlanList = (params: {
  page?: number;
  size?: number;
  keyword?: string;
  creator?: string;
  fromTime?: string;
  toTime?: string;
}) => {
  return request({
    url: '/api/sm/circuitPlan',
    method: 'get',
    params
  });
};
/**
 * 查询计划周期列表
 * @param params
 * @returns
 */
export const GetPatrolPeriodPlanList = () => {
  return request({
    url: '/api/sm/circuitPlan/circleList',
    method: 'get'
  });
};
/**
 * 添加巡检计划
 * @param params
 * @returns
 */
export const AddPatrolPlan = (params: {
  name: string;
  districtAreaId: string;
  isNormalPlan: string;
  isNeedFeedback: string;
  moveType: string;
  devices: string;
  specialDevices: string;
  planCircle: string;
  remark: string;
  inspectionConfigId?: string;
}) => {
  return request({
    url: '/api/sm/circuitPlan',
    method: 'post',
    data: params
  });
};
/**
 * 计划任务
 * @param params
 * @returns
 */
export const PlanPatrolTask = (params: {
  planId: string;
  name: string;
  receiveUserId: string;
  beginTime: string;
  endTime: string;
  collaborateUserId?: string;
  presentDistance?: string;
  remark?: string;
}) => {
  return request({
    url: '/api/sm/circuitPlan/plan',
    method: 'post',
    data: params
  });
};

/**
 * 查询巡检任务报告
 * @param params
 * @returns
 */
export const GetPatrolReport = (params: {
  taskCode: string;
  page?: number;
  size?: number;
  fromTime?: string;
  toTime?: string;
  type?: string;
}) => {
  return request({
    url: '/api/sm/circuitTaskReport',
    method: 'get',
    params
  });
};

/**
 * 批量分派巡检任务
 * @param params
 * @returns
 */
export const DiapatchPatrolTasks = (params: {
  /** 任务id */
  taskIdList?: string[];
  /** 接收人员id */
  receiveUserId?: string;
  /** 共同完成人id，多个用逗号隔开 */
  collaborateUserId?: string;
}) => {
  return request({
    url: '/api/sm/circuitTask/assign',
    method: 'post',
    data: params
  });
};
/**
 * 上报巡检工单
 * @param params
 * @returns
 */
export const PostWorkOrder = (params: {
  title?: string;
  level?: string;
  type?: string;
  code?: string;
  remark?: string;
}) => {
  return request({
    url: '/api/sm/circuitTask/workOrder',
    method: 'post',
    data: params
  });
};

/**
 * 批量删除巡检任务
 * @param ids
 * @returns
 */
export const DeletePatrolTasks = (ids: string[]) => {
  return request({
    url: '/api/sm/circuitTask',
    method: 'delete',
    data: ids
  });
};
/**
 * 查询巡检任务列表
 * @param params
 * @returns
 */
export const GetPatrolTaskList = (params: {
  page?: number;
  size?: number;
  fromTime?: string;
  toTime?: string;
  isNormalPlan?: string;
  isReceived?: string;
  beginTimeFrom?: string;
  beginTimeTo?: string;
  receiveUserId?: string;
  creator?: string;
  collaborateUserId?: string;
  keyword?: string;
}) => {
  return request({
    url: '/api/sm/circuitTask',
    method: 'get',
    params
  });
};
/**
 * 查询巡检完成统计
 * @returns
 */
export const GetPatrolCompleteCount = () => {
  return request({
    url: `/api/sm/circuitTask/completeCount`,
    method: 'get'
  });
};

/**
 * 审核巡检任务
 * @param params
 * @returns
 */
export const VerifyPatrolTask = (params: {
  taskIdList: string[];
  status: string;
  rejectReason?: string;
}) => {
  return request({
    url: '/api/sm/circuitTask/audit',
    method: 'post',
    data: params
  });
};

/**
 * 获取已完成任务总数
 * @returns
 */
export const GetPatrolCompleteTotal = () => {
  return request({
    url: '/api/sm/circuitTask/completeTotal',
    method: 'get'
  });
};

/**
 * 获取到位率
 * @returns
 */
export const GetPatrolArrivalRate = () => {
  return request({
    url: '/api/sm/circuitTask/arrivalRate',
    method: 'get'
  });
};

/**
 * 获取反馈率
 * @returns
 */
export const GetPatrolFeedbackRate = () => {
  return request({
    url: '/api/sm/circuitTask/feedbackRate',
    method: 'get'
  });
};
