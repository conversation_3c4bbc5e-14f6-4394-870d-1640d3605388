<template>
  <div>
    <div class="detail-basic">
      <InlineForm
        ref="refFormBasic"
        :config="FormConfigBasic"
      ></InlineForm>
    </div>
    <div class="detail-taskinfo">
      <div class="detail-group-title">
        任务详情
      </div>
      <FormTable :config="TableConfigTaskInfo"></FormTable>
    </div>
    <div class="detail-response">
      <p class="detail-group-title">
        反馈信息
      </p>
      <Tabs
        v-model="state.curResponse"
        :config="{
          type: 'tabs',
          tabs: [
            { label: '关键点', value: '关键点' },
            { label: '设备', value: '设备' },
            { label: '专项设备', value: '专项设备' },
            { label: '巡检表单', value: '巡检表单' }
          ]
        }"
        @change="refreshResponse"
      ></Tabs>
      <!-- 原有的反馈信息内容 -->
      <div v-if="state.curResponse !== '巡检表单'" class="detail-response-tabcontent">
        <div class="left">
          <FormTable :config="TableConfigResponse"></FormTable>
        </div>
        <div class="right">
          <div class="right-header">
            <span>媒体信息 - {{ TableConfigResponse.currentRow?.name }}</span>
            <!-- <el-button
              :icon="Promotion"
              size="small"
              style="margin-left: auto"
              @click="handleKeyPointReport"
            >
              {{ (TableConfigResponse.currentRow?.type || '') + '上报' }}
            </el-button> -->
          </div>
          <div class="right-item">
            <span>图片</span>
            <div class="box overlay-y">
              <el-image
                v-for="(image, i) in images"
                :key="i"
                :fit="'cover'"
                :src="image"
                :alt="image"
                :preview-src-list="images"
                :initial-index="i"
              ></el-image>
            </div>
          </div>
          <div class="right-item">
            <span>视频</span>
            <div class="box videos overlay-y">
              <div
                v-for="(video, i) in videos"
                :key="i"
                class="video-box"
              >
                <Videor
                  class="video"
                  :url="video"
                ></Videor>
              </div>
            </div>
          </div>
          <div class="right-item">
            <span>音频</span>
            <div class="box overlay-y">
              <div
                v-for="(audio, i) in audios"
                :key="i"
                class="audio-item"
              >
                <Voicer
                  :url="audio"
                  :show-url="true"
                  :download="false"
                ></Voicer>
              </div>
            </div>
          </div>
          <div class="right-item">
            <span>附件</span>
            <div class="box overlay-y">
              <div
                v-for="(file, i) in files"
                :key="i"
                class="file-item"
              >
                <el-icon>
                  <Document></Document>
                </el-icon>
                <span class="url">{{ file }}</span>
                <el-icon class="download-icon">
                  <Download @click="downloadFile(file)"></Download>
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 巡检表单内容 -->
      <div v-if="state.curResponse === '巡检表单'" class="detail-form-content">
        <FormTable :config="TableConfigFormRecord"></FormTable>
      </div>
    </div>
    <DialogForm
      ref="refDialogForm"
      :config="DialogFormConfig"
    ></DialogForm>
    <DialogForm
      ref="refDialogFormReport"
      :config="DialogFormConfigReport"
    ></DialogForm>
    <DialogForm
      ref="refDialogFormRecord"
      :config="DialogFormConfigFormRecord"
    ></DialogForm>
  </div>
</template>
<script lang="ts" setup>
import { Download, Edit, Document } from '@element-plus/icons-vue'
import { GetPatrolReport, PostWorkOrder } from '@/api/patrol'
import { downloadFile } from '@/utils/fileHelper'
import { SLMessage } from '@/utils/Message'
import { getFormRecordsByTaskId, updateCheckResult } from '@/api/smartManagement/circuitTaskFormRecord'
import { useUserStore } from '@/store'
import { PatrolTaskStatusConfig } from '../../config'
import { excuteQuery, getGeometryCenterPoint, gotoAndHighLight, initQueryParams, setSymbol } from '@/utils/MapHelper'
import { useDistrict } from '../../hooks/useDistrict'
import { useWaterPoint } from '@/hooks/arcgis'
import { getEmergencyLevelOpetions } from '@/views/workorder/config'
import useCTI from '@/hooks/CTI/useCTI'
import { workOrderTypeList } from '@/api/CTI/systemConfiguration'
import { formatTree } from '@/utils/GlobalHelper'

const refDialogFormReport = ref<IDialogFormIns>()
const refDialogForm = ref<IDialogFormIns>()
const refDialogFormRecord = ref<IDialogFormIns>()
const refFormBasic = ref<IInlineFormIns>()

const userStore = useUserStore()
const props = defineProps<{
  row?: any
  view?: __esri.MapView
  layerInfo?: any[]
}>()
const emit = defineEmits(['row-click'])
const state = reactive<{
  curResponse: string
  responses: any[]
  orderTypes: any[]
  formRecords: any[]
}>({
  curResponse: '关键点',
  responses: [],
  orderTypes: [],
  formRecords: []
})

const FormConfigBasic = reactive<IFormConfig>({
  group: [
    {
      fields: [
        {
          type: 'tag',
          label: '任务名称：',
          field: 'name'
        },
        // { type: 'tag', label: '巡检类型：', field: 'type' },
        { type: 'text', label: '巡检员：', field: 'inspector' },
        { type: 'text', label: '共同处理人：', field: 'collaborateUserName' },
        { type: 'text', label: '到位距离：', field: 'distance', unit: '米' },
        {
          type: 'text',
          label: '任务描述：',
          field: 'remark'
        },
        {
          type: 'btn-group',
          btns: [
            {
              perm: false,
              svgIcon: shallowRef(Edit),
              isTextBtn: true,
              click: () => handleRemarkEdit()
            }
          ]
        }
      ]
    }
  ],
  defaultValue: {},
  labelWidth: 100,
  labelPosition: 'right'
})
const TableConfigTaskInfo = reactive<ITable>({
  dataList: [],
  columns: [
    { minWidth: 120, label: '任务编号', prop: 'code' },
    { minWidth: 120, label: '任务名称', prop: 'name' },
    { minWidth: 160, label: '开始时间', prop: 'beginTime' },
    { minWidth: 160, label: '结束时间', prop: 'endTime' },
    { minWidth: 120, label: '关键设备数目', prop: 'deviceCount' },
    { minWidth: 120, label: '关键点数目', prop: 'keyPointCount' },
    // minWidth: 120,  { label: '查看状态', prop: 'key1' },
    {
      minWidth: 120,
      label: '任务状态',
      prop: 'status',
      formatter: (row, val) => PatrolTaskStatusConfig[val]?.text
    },
    { minWidth: 120, label: '到位状况', prop: 'presentState' },
    { minWidth: 120, label: '反馈状况', prop: 'fallbackState' }
  ],
  pagination: {
    hide: true
  }
})

const TableConfigResponse = reactive<ITable>({
  dataList: [],
  columns: [
    {
      label: '设备类型',
      prop: 'name',
      hidden: computed((): boolean => state.curResponse === '关键点') as any
    },
    {
      label: '设备编号',
      prop: 'device_type',
      hidden: computed((): boolean => state.curResponse === '关键点') as any
    },
    {
      label: '名称',
      prop: 'name',
      hidden: computed((): boolean => state.curResponse !== '关键点') as any
    },
    {
      label: '是否到位',
      prop: 'is_settle',
      formatter: (row, val) => (val === 'true' ? '是' : '否')
    },
    { label: '到位日期', prop: 'settle_time' },
    {
      label: '是否反馈',
      prop: 'is_fallback',
      formatter: (row, val) => (val === 'true' ? '是' : '否')
    },
    { label: '反馈日期', prop: 'fallback_time' }
  ],
  pagination: {
    align: 'right',
    refreshData: ({ page, size }) => {
      TableConfigResponse.pagination.page = page || 1
      TableConfigResponse.pagination.limit = size || 20
      refreshResponse()
    }
  },
  handleRowClick: async row => {
    TableConfigResponse.currentRow = row
    emit('row-click', row)
    setTimeout(() => {
      locateTo()
    }, 500)
  }
})

// 表单记录表格配置
const TableConfigFormRecord = reactive<ITable>({
  dataList: [],
  columns: [
    { minWidth: 120, label: '巡检地点', prop: 'location' },
    { minWidth: 120, label: '巡检位置', prop: 'position' },
    { minWidth: 200, label: '检验内容', prop: 'content' },
    {
      minWidth: 100,
      label: '检验结果',
      prop: 'result',
      formatter: (row, val) => {
        const resultMap = {
          'NORMAL': '正常',
          'ABNORMAL': '异常',
          'NOT_CHECKED': '未检查'
        }
        return resultMap[val] || val
      }
    },
    { minWidth: 150, label: '结果描述', prop: 'resultDescription' },
    { minWidth: 120, label: '检验时间', prop: 'checkTime', formatter: (row, val) => {
      return val ? new Date(val).toLocaleString() : '-'
    }}
  ],
  operationWidth: 120,
  operations: [
    {
      perm: true,
      text: '填写',
      type: 'primary',
      click: (row: any) => handleFormRecordEdit(row)
    }
  ],
  pagination: {
    hide: true
  }
})

const waterPoint = useWaterPoint('viewDiv')
const locateTo = async () => {
  if (!state.curResponse) return
  const id = TableConfigResponse.currentRow.deviceType
  if (id === undefined) {
    // SLMessage.info('暂无位置信息，无法定位')
    return
  }
  if (state.curResponse === '关键点') {
    district.extentTo('point', id)
    waterPoint.removeAll()
  } else if (state.curResponse === '设备') {
    const layerId = props.layerInfo?.find(item => item.layername === TableConfigResponse.currentRow.name)?.layerid
    if (layerId === undefined) return
    const devices = await excuteQuery(
      window.SITE_CONFIG.GIS_CONFIG.gisService + window.SITE_CONFIG.GIS_CONFIG.gisPipeDataService + '/' + layerId,
      initQueryParams({
        where: 'OBJECTID=' + id
      })
    )
    const device = devices?.features?.[0]
    if (!device) return
    device.symbol = setSymbol(device.geometry.type, {
      color: [0, 0, 0, 0],
      outlineWidth: 2,
      outlineColor: [0, 0, 0, 0]
    })
    waterPoint.removeAll()
    district.removeHighlight()
    waterPoint.add(props.view, {
      id,
      point: getGeometryCenterPoint(device.geometry)
    })
    gotoAndHighLight(props.view, devices?.features?.[0], {
      avoidHighlight: true
    })
  }
}
const images = computed(() => {
  return TableConfigResponse.currentRow?.image?.split(',') || []
})
const videos = computed(() => {
  return TableConfigResponse.currentRow?.image?.split(',') || []
})
const audios = computed(() => {
  return TableConfigResponse.currentRow?.audio?.split(',') || []
})
const files = computed(() => {
  return TableConfigResponse.currentRow?.file?.split(',') || []
})
const refreshResponse = () => {
  if (state.curResponse === '巡检表单') {
    refreshFormRecords()
  } else {
    GetPatrolReport({
      taskCode: props.row.code,
      page: TableConfigResponse.pagination.page || 1,
      size: TableConfigResponse.pagination.limit || 20,
      type: state.curResponse
    }).then(res => {
      state.responses = res.data.data?.data
      TableConfigResponse.dataList = res.data?.data?.data || []
      TableConfigResponse.pagination.total = res.data?.data?.total || 0
    })
  }
}

// 刷新表单记录
const refreshFormRecords = () => {
  if (!props.row?.id) return

  getFormRecordsByTaskId(props.row.id).then(res => {
    // API返回结构是 {code, message, data}，实际数据在 res.data.data 中
    const records = res.data?.data || []
    state.formRecords = Array.isArray(records) ? records : []
    TableConfigFormRecord.dataList = state.formRecords

    // 临时调试日志，确认数据获取情况
    if (records.length > 0) {
      console.log('成功获取表单记录:', records.length, '条')
    } else {
      console.log('未获取到表单记录数据')
    }
  }).catch(error => {
    console.error('获取表单记录失败:', error)
    // 设置为空数组，避免后续处理出错
    state.formRecords = []
    TableConfigFormRecord.dataList = []
    SLMessage.error('获取表单记录失败')
  })
}

// 处理表单记录编辑
const handleFormRecordEdit = (row: any) => {
  DialogFormConfigFormRecord.defaultValue = {
    id: row.id,
    location: row.location,
    position: row.position,
    content: row.content,
    result: row.result,
    resultDescription: row.resultDescription,
    attachments: row.attachments
  }
  refDialogFormRecord.value?.openDialog()
  nextTick(() => {
    refDialogFormRecord.value?.resetForm()
  })
}

const handleRemarkEdit = async () => {
  refDialogForm.value?.openDialog()
  DialogFormConfig.defaultValue = {
    ...(props.row || {})
  }
  await nextTick()
  refDialogForm.value?.resetForm()
}
const DialogFormConfig = reactive<IDialogFormConfig>({
  dialogWidth: 500,
  title: '描述',
  labelPosition: 'top',
  group: [
    {
      fields: [{ type: 'textarea', field: 'remark', maxRow: 10, minRow: 10 }]
    }
  ],
  submit: params => {
    console.log(params)
    refDialogForm.value?.closeDialog()
  }
})

// const handleKeyPointReport = async () => {
//   if (!TableConfigResponse.currentRow) {
//     SLMessage.warning('请先选择一行反馈信息')
//     return
//   }
//   DialogFormConfigReport.defaultValue = {
//     code: props.row?.code
//   }
//   refDialogFormReport.value?.openDialog()
//   await nextTick()
//   refDialogFormReport.value?.resetForm()
// }
const DialogFormConfigReport = reactive<IDialogFormConfig>({
  dialogWidth: 450,
  labelPosition: 'right',
  title: '事件上报',
  group: [
    {
      fields: [
        {
          rules: [{ required: true, message: '请输入工单标题' }],
          type: 'input',
          label: '工单标题',
          field: 'title'
        },
        {
          rules: [{ required: true, message: '请选择紧急程度' }],
          type: 'select',
          label: '紧急程度',
          field: 'level',
          options: getEmergencyLevelOpetions()
        },
        {
          rules: [{ required: true, message: '请选择工单类型' }],
          type: 'select-tree',
          label: '工单类型',
          field: 'type',
          async autoFillOptions(config) {
            try {
              const res = await workOrderTypeList({ isDel: 0 })
              config.options = formatTree(res?.data?.data || [], {
                label: 'name',
                value: 'id',
                children: 'children',
                id: 'id'
              })
            } catch (error) {
              //
            }
          }
        },
        { type: 'textarea', label: '描述', field: 'remark' }
      ]
    }
  ],
  defaultValue: {},
  submit: params => {
    PostWorkOrder({ ...params, pointId: TableConfigResponse.currentRow.id })
      .then(res => {
        if (res.data.code === 200) {
          SLMessage.success(res.data.message)
          refDialogFormReport.value?.closeDialog()
        } else {
          SLMessage.error(res.data.message)
        }
      })
      .catch((error: any) => {
        console.log(error)
        SLMessage.error('系统错误')
      })
  }
})

// 表单记录编辑对话框配置
const DialogFormConfigFormRecord = reactive<IDialogFormConfig>({
  dialogWidth: 600,
  title: '填写巡检表单',
  labelPosition: 'right',
  labelWidth: 100,
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '巡检地点',
          field: 'location',
          readonly: true
        },
        {
          type: 'input',
          label: '巡检位置',
          field: 'position',
          readonly: true
        },
        {
          type: 'textarea',
          label: '检验内容',
          field: 'content',
          readonly: true,
          maxRow: 3,
          minRow: 3
        },
        {
          type: 'radio',
          label: '检验结果',
          field: 'result',
          rules: [{ required: true, message: '请选择检验结果' }],
          options: [
            { label: '正常', value: 'NORMAL' },
            { label: '异常', value: 'ABNORMAL' }
          ]
        },
        {
          type: 'textarea',
          label: '结果描述',
          field: 'resultDescription',
          maxRow: 4,
          minRow: 4
        },
        {
          type: 'upload',
          label: '附件',
          field: 'attachments',
          multiple: true,
          accept: '.jpg,.jpeg,.png,.pdf,.doc,.docx'
        }
      ]
    }
  ],
  defaultValue: {},
  submit: async (params) => {
    try {
      await updateCheckResult(params.id, {
        result: params.result,
        resultDescription: params.resultDescription,
        attachments: params.attachments,
        checkUserId: userStore.userInfo?.id || ''
      })
      SLMessage.success('保存成功')
      refDialogFormRecord.value?.closeDialog()
      refreshFormRecords()
    } catch (error) {
      console.error('保存失败:', error)
      SLMessage.error('保存失败')
    }
  }
})

const district = useDistrict('viewDiv')
watch(
  () => props.row,
  () => {
    refreshData()
  }
)
const refreshData = () => {
  if (!props.row) return

  // 初始化表单记录数据为空数组
  state.formRecords = []
  TableConfigFormRecord.dataList = []

  refreshResponse()
  refreshFormRecords() // 初始化表单记录
  TableConfigTaskInfo.dataList = [props.row]
  if (!refFormBasic.value) return
  refFormBasic.value.dataForm = {
    name: props.row.name,
    inspector: props.row.receiveUserName,
    collaborateUserName: props.row.collaborateUserName,
    presentDistance: props.row.presentDistance,
    remark: props.row.remark
  }
  district.removeAll()
  district.add(props.view, props.row?.districtAreaId, {
    ratio: 1,
    highlight: false,
    showKeyPoint: true
  })
}
const { getOrderTypeOption } = useCTI()
const initOrderTypes = async () => {
  state.orderTypes = await getOrderTypeOption()
}
onMounted(() => {
  refreshData()
  initOrderTypes()
})
onBeforeUnmount(() => {
  district.destroy()
})
</script>
<style lang="scss" scoped>
.detail-taskinfo {
  padding-bottom: 30px;
}
.detail-group-title {
  font-size: 16px;
  line-height: 32px;
  font-weight: bold;
}
.detail-response-tabcontent {
  height: 100%;
  display: flex;
  flex-direction: row;
  .left,
  .right {
    padding: 8px;
    height: 600px;
  }
  .left {
    width: 60%;
    // display: flex;
    // flex-direction: column;
  }
  .right {
    width: 40%;
    .right-header {
      display: flex;
      align-items: center;
      height: 40px;
      font-size: 16px;
      font-weight: bold;
    }
    .right-item {
      & > span {
        line-height: 36px;
        font-size: 14px;
      }
      .box {
        height: 100px;
        // border: 1px solid var(--el-border-color);
        box-shadow: 0 0 0 1px inset var(--el-border-color);
        padding: 4px;
        .el-image {
          height: 100%;
          min-width: 90px;
          vertical-align: bottom;
          padding: 4px;
        }
        &.videos {
          height: 196px;
          min-height: 100px;
          .video-box {
            padding: 4px;
            display: inline-flex;
          }
        }
        .audio-item {
          padding: 4px 4px 8px;
        }
        .file-item {
          line-height: 28px;
          padding: 0 20px 4px 4px;
          display: flex;
          align-items: center;
          .url {
            margin-right: auto;
            padding-left: 4px;
          }
          .download-icon {
            cursor: pointer;
          }
          &:hover {
            background-color: var(--el-border-color);
          }
        }
      }
    }
  }
}

.detail-form-content {
  padding: 8px;
  height: 600px;
}
</style>
