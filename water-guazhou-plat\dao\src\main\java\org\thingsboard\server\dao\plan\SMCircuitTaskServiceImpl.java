package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.dao.DaoUtil;
import org.thingsboard.server.dao.model.sql.department.Organization;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.*;
import org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.model.sql.statistic.StatisticItem;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrder;
import org.thingsboard.server.dao.orderWork.NewlyWorkOrderService;
import org.thingsboard.server.dao.sql.department.DepartmentMapper;
import org.thingsboard.server.dao.sql.department.OrganizationMapper;
import org.thingsboard.server.dao.sql.smartManagement.plan.SMCircuitTaskMapper;
import org.thingsboard.server.dao.sql.user.UserMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.TreeQueryOptions;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.*;
import org.thingsboard.server.dao.util.imodel.query.workOrder.WorkOrderSaveRequest;

import java.util.Collections;
import java.util.List;

import static org.thingsboard.server.common.data.DataConstants.PEOPLE_TYPE.XUN_JIAN_REN_YUAN;
import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.APPROVED;
import static org.thingsboard.server.dao.model.sql.smartProduction.circuit.GeneralTaskStatus.RECEIVED;

@Service
public class SMCircuitTaskServiceImpl implements SMCircuitTaskService {
    @Autowired
    private SMCircuitTaskMapper mapper;

    @Autowired
    private NewlyWorkOrderService workOrderService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CircuitTaskReportService reportService;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private SMCircuitTaskFormRecordService formRecordService;

    @Override
    public IPage<SMCircuitTaskResponse> findAllConditional(SMCircuitTaskPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SMCircuitTask save(SMCircuitTaskSaveRequest entity) {
        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    public List<SMCircuitTask> saveAll(List<SMCircuitTaskSaveRequest> entities) {
        return QueryUtil.saveOrUpdateBatchByRequest(entities, mapper::saveAll, mapper::saveAll);
    }

    @Override
    public boolean update(SMCircuitTask entity) {
        return mapper.update(entity);
    }

    @Override
    public boolean delete(String id) {
        // 关联删除已由数据库Trigger实现
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean assign(SMCircuitTaskAssignRequest req) {
        return mapper.assign(req);
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        // 关联删除已由数据库Trigger实现
        return mapper.deleteBatchIds(idList) > 0;
    }

    @Override
    public boolean assignBatch(SMCircuitTaskBatchAssignRequest req) {
        //TODO 2025/4/28 改为待审核状态，审核之后可以下发
        return mapper.assignBatch(req) > 0;
    }

    //分派任务审核
    @Override
    public boolean auditBatch(SMCircuitTaskBatchAuditRequest req) {
        return mapper.auditBatch(req) > 0;
    }

    @Override
    @Transactional
    public boolean report(SMCircuitTaskReportRequest req) {
        String pointId = req.getPointId();
        Boolean isPitfall = req.getIsPitfall();

        if (pointId != null) {
            // 报告关键点 同时到位、反馈
            String reportId = mapper.getReportId(req.getCode(), pointId, req.getTenantId());
            CircuitTaskReportCompleteRequest markRequest = CircuitTaskReportCompleteRequest.fromSMCircuitTaskReportRequest(
                    req, reportId);
            reportService.present(markRequest);
            reportService.fallback(markRequest);
        }

        // 如果有隐患 则创建工单
        if (isPitfall) {
            WorkOrderSaveRequest saveTemplate = req.buildWorkOrderSaveTemplate();
            saveTemplate.setIsDirectDispatch(Boolean.toString(req.getProcessLevel() != null));
            WorkOrder save = workOrderService.save(saveTemplate);
            return mapper.relateWorkOrder(req.getCode(), save.getSerialNo(), pointId, req.getTenantId());
        }

        return true;
    }

    @Override
    public IPage<SMCircuitTaskWorkOrderPitfallResponse> findPitfallWorkorderInfo(SMCircuitTaskWorkOrderPitfallPageRequest request) {
        return mapper.findPitfallWorkorderInfo(request);
    }

    @Override
    public StatisticItem findWorkOrderTrend(SMCircuitTaskWorkOrderTrendRequest req) {
        return QueryUtil.statistic(req.getTimeUnit(), req,
                (from, to) -> mapper.workOrderCountByTypeTimed((SMCircuitTaskWorkOrderTrendRequest) req.changeTimeRange(from, to)));
    }

    @Override
    public StatisticItem countByUser(SMCircuitTaskWorkOrderStatisticUserRequest req) {
        return QueryUtil.statisticByTimeThenKey(req, (from, to) -> mapper.workOrderCountByUserTimed(req));
    }

    @Override
    public boolean complete(SMCircuitTaskCompleteRequest req) {
        return mapper.complete(req);
    }

    @Override
    public List<String> getBatchCode(List<String> idList) {
        return mapper.getBatchCode(idList);
    }

    @Override
    public GeneralTaskStatusStatistic countStatusByUser(String userId, GeneralTaskStatus status) {
        return countStatusByUser(userId, Collections.singletonList(status));
    }

    @Override
    public GeneralTaskStatusStatistic countStatusByUser(String userId, List<GeneralTaskStatus> status) {
        return new GeneralTaskStatusStatistic(
                mapper.totalStatusOfUser(userId, status),
                mapper.totalOfUser(userId)
        );
    }

    @Override
    public GeneralTaskProcessingAndCompleteCount SMCircuitTaskProcessingAndCompleteCount(String userId) {
        return new GeneralTaskProcessingAndCompleteCount(
                mapper.totalStatusOfUser(userId, Collections.singletonList(RECEIVED)),
                mapper.totalStatusOfUser(userId, Collections.singletonList(APPROVED))
        );
    }

    @Override
    public SMCircuitTaskWorkOrderPitfallResponse findPitfallWorkorderInfoByPointId(String taskCode, String pointId, String tenantId) {
        return mapper.findPitfallWorkorderInfoByPointId(taskCode, pointId, tenantId);
    }

    @Override
    public SMCircuitTaskResponse findById(String id) {
        return mapper.findById(id);
    }

    @Override
    public boolean isComplete(String code, String tenantId) {
        return mapper.isComplete(code, tenantId);
    }

    @Override
    public boolean canBeComplete(String id) {
        // 检查原有的完成条件
        boolean canComplete = mapper.canBeComplete(id);

        if (canComplete) {
            // 检查表单是否全部完成
            boolean formCompleted = formRecordService.isTaskFormCompleted(id);
            return formCompleted;
        }

        return false;
    }

    @Override
    public List<Organization> findGroupedUser(String tenantId, DataConstants.PEOPLE_TYPE type) {
        return QueryUtil.buildTree(
                organizationMapper.findRoots(tenantId),
                TreeQueryOptions.withPure(true).prune(true)
                        .treeLabels("Organization", "Department", "User")
                        .nameMappings(null, null, "firstName"),
                pid -> organizationMapper.findChildren(pid, tenantId),
                pid -> departmentMapper.findChildren(pid, tenantId),
                pid -> DaoUtil.convertDataList(userMapper.findUserByGisUserTypeAndDepartmentId(pid, type, tenantId))
        );
    }

    @Override
    public SMCircuitUserStatistic statisticUser(String tenantId, DataConstants.PEOPLE_TYPE type) {
        return new SMCircuitUserStatistic(
                mapper.countGisUser(XUN_JIAN_REN_YUAN, tenantId),
                countSpecifyUser(tenantId, type)
        );
    }

    private int countSpecifyUser(String tenantId, DataConstants.PEOPLE_TYPE type) {
        switch (type) {
            case XUN_JIAN_REN_YUAN:
                mapper.countOutsideCircuitUser(tenantId);
                break;
            case QIANG_XIU_REN_YUAN:
                // TODO: [LFT] 抢修人员相关统计
                break;
            case CHAO_BIAO_YUAN:
                mapper.countCustomerCount(tenantId);
                break;
        }

        return -1;
    }


    @Override
    public Integer completeTotal() {
        int i = mapper.completeTotal();
        return i;
    }

    @Override
    public String arrivalRate() {
        String s = reportService.arrivalRate();
        return s;
    }

    @Override
    public String feedbackRate() {
        String s = reportService.feedbackRate();
        return s;
    }


}
