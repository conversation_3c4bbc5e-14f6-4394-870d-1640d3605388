package org.thingsboard.server.dao.plan;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecordResponse;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskFormRecordPageRequest;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitTaskFormRecordSaveRequest;

import java.util.List;

/**
 * 巡检任务表单记录服务接口
 */
public interface SMCircuitTaskFormRecordService {

    /**
     * 分页条件查询表单记录
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<SMCircuitTaskFormRecordResponse> findAllConditional(SMCircuitTaskFormRecordPageRequest request);

    /**
     * 根据任务ID查询表单记录
     *
     * @param taskId 任务ID
     * @return 表单记录列表
     */
    List<SMCircuitTaskFormRecordResponse> findByTaskId(String taskId);

    /**
     * 根据任务编号查询表单记录
     *
     * @param taskCode 任务编号
     * @return 表单记录列表
     */
    List<SMCircuitTaskFormRecordResponse> findByTaskCode(String taskCode);

    /**
     * 保存表单记录
     *
     * @param entity 实体信息
     * @return 保存好的实体
     */
    SMCircuitTaskFormRecord save(SMCircuitTaskFormRecordSaveRequest entity);

    /**
     * 批量保存
     *
     * @param entities 实体列表
     * @return 保存好的数据
     */
    List<SMCircuitTaskFormRecord> saveAll(List<SMCircuitTaskFormRecordSaveRequest> entities);

    /**
     * 增量修改
     *
     * @param entity 实体信息
     * @return 是否修改成功
     */
    boolean update(SMCircuitTaskFormRecord entity);

    /**
     * 删除
     *
     * @param id 唯一标识
     * @return 是否删除成功
     */
    boolean delete(String id);

    /**
     * 批量删除
     *
     * @param idList id列表
     * @return 是否成功
     */
    boolean deleteAll(List<String> idList);

    /**
     * 根据巡检配置生成任务表单记录
     *
     * @param taskId 任务ID
     * @param taskCode 任务编号
     * @param inspectionConfigId 巡检配置ID
     * @param formConfig 表单配置JSON
     * @param tenantId 租户ID
     * @return 生成的记录数量
     */
    int generateFormRecords(String taskId, String taskCode, String inspectionConfigId, String formConfig, String tenantId);

    /**
     * 更新检验结果
     *
     * @param id 记录ID
     * @param result 检验结果
     * @param resultDescription 结果描述
     * @param attachments 附件
     * @param checkUserId 检验人员ID
     * @return 是否更新成功
     */
    boolean updateCheckResult(String id, String result, String resultDescription, String attachments, String checkUserId);

    /**
     * 批量更新检验结果
     *
     * @param records 表单记录列表
     * @return 是否更新成功
     */
    boolean batchUpdateCheckResult(List<SMCircuitTaskFormRecord> records);

    /**
     * 根据任务ID删除表单记录
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteByTaskId(String taskId);

    /**
     * 检查任务表单是否全部完成
     *
     * @param taskId 任务ID
     * @return 是否全部完成
     */
    boolean isTaskFormCompleted(String taskId);
}
