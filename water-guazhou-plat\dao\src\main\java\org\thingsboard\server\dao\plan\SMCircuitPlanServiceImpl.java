package org.thingsboard.server.dao.plan;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.circuitSettings.CircuitSettingsService;
import org.thingsboard.server.dao.district.CircuitDistrictPointService;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlan;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitPlanResponse;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTask;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskPlanRequest;
import org.thingsboard.server.dao.model.sql.smartManagement.settings.CircuitSettings;
import org.thingsboard.server.dao.sql.smartManagement.plan.SMCircuitPlanMapper;
import org.thingsboard.server.dao.util.imodel.query.QueryUtil;
import org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.*;
import org.thingsboard.server.dao.util.reflection.ExceptionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.thingsboard.server.dao.util.imodel.query.smartManagement.plan.SMCircuitPlanDeviceSaveRequest.DEVICE_SPECIFIER;

@Service
public class SMCircuitPlanServiceImpl implements SMCircuitPlanService {
    @Autowired
    private SMCircuitPlanMapper mapper;

    @Autowired
    private SMCircuitTaskService service;

    @Autowired
    private CircuitTaskReportService reportService;

    @Autowired
    private CircuitDistrictPointService pointService;

    @Autowired
    private SMCircuitTaskFormRecordService formRecordService;

    @Autowired
    private CircuitSettingsService circuitSettingsService;

    @Override
    public IPage<SMCircuitPlanResponse> findAllConditional(SMCircuitPlanPageRequest request) {
        return mapper.findByPage(request);
    }

    @Override
    public SMCircuitPlan save(SMCircuitPlanSaveRequest entity) {
        if (entity.getDevices().size() == 0 && !pointService.hasPoint(entity.getDistrictAreaId())){
            ExceptionUtils.silentThrow("没有可以巡检的点或设备");
        }

        return QueryUtil.saveOrUpdateOneByRequest(entity, mapper::insert, mapper::update);
    }

    @Override
    @Transactional
    public int plan(SMCircuitTaskPlanRequest req) {
        SMCircuitPlan plan = mapper.selectById(req.getPlanId());
        // 批量创建任务
        List<SMCircuitTaskSaveRequest> itemTemplates = req.build(plan);
        // jdbc支持最大插入数
        ExecutorService executorService = Executors.newCachedThreadPool();
        List<SMCircuitTask> tasks = new CopyOnWriteArrayList<>();
        int maxNum = itemTemplates.size() / 1343 + 1;
        if (itemTemplates.size() > 0) {
            for (int i = 0; i < maxNum; i++) {
                int finalI = i;
                executorService.submit(() -> {
                    tasks.addAll(service.saveAll(itemTemplates.stream().skip(finalI * 1343).limit(1343).collect(Collectors.toList())));
                });
            }
        }

        executorService.shutdown();
        while (!executorService.isTerminated()) {

        }

        // 生成报告
        List<String> idList = tasks.stream().map(SMCircuitTask::getId).collect(Collectors.toList());
        List<String> codeList = service.getBatchCode(idList);
        List<SMCircuitPlanDeviceSaveRequest> devices = JSONArray.parseArray(plan.getDevices(), SMCircuitPlanDeviceSaveRequest.class);
        List<CircuitTaskReportSaveRequest> reports = Stream.concat(
                pointService.selectReportTemplate(idList).stream(),
                codeList.stream().flatMap(
                        code -> new ArrayList<>(devices).stream().map(x -> x.build(DEVICE_SPECIFIER, code))
                )
        ).collect(Collectors.toList());
        for (CircuitTaskReportSaveRequest report : reports) {
            report.currentUserId(req.currentUserUUID());
            report.tenantId(req.tenantId());
        }

        executorService = Executors.newCachedThreadPool();
        maxNum = reports.size() / 1343 + 1;
        if (reports.size() > 0) {
            for (int i = 0; i < maxNum; i++) {
                int finalI = i;
                executorService.submit(() -> {
                    reportService.saveAll(reports.stream().skip(finalI * 1343).limit(1343).collect(Collectors.toList()));
                });
            }
        }

        executorService.shutdown();
        while (!executorService.isTerminated()) {

        }

        // 生成表单记录（如果巡检计划关联了巡检配置）
        if (plan.getInspectionConfigId() != null && !plan.getInspectionConfigId().trim().isEmpty()) {
            try {
                CircuitSettings circuitSettings = circuitSettingsService.findById(plan.getInspectionConfigId());
                if (circuitSettings != null && circuitSettings.getFormConfig() != null && !circuitSettings.getFormConfig().trim().isEmpty()) {
                    // 为每个任务生成表单记录，使用已获取的codeList
                    for (int i = 0; i < tasks.size() && i < codeList.size(); i++) {
                        SMCircuitTask task = tasks.get(i);
                        String taskCode = codeList.get(i);

                        formRecordService.generateFormRecords(
                            task.getId(),
                            taskCode,
                            plan.getInspectionConfigId(),
                            circuitSettings.getFormConfig(),
                            task.getTenantId()
                        );
                    }
                }
            } catch (Exception e) {
                // 记录日志但不影响任务创建
                System.err.println("生成表单记录失败: " + e.getMessage());
            }
        }

        return itemTemplates.size();
    }

    @Override
    public boolean isPlanArranged(String planId, Date beginTime, Date endTime) {
        return mapper.isPlanArranged(planId, beginTime, endTime);
    }

    @Override
    public boolean update(SMCircuitPlan entity) {
        return mapper.update(entity);
    }

    @Override
    @Transactional
    public boolean delete(String id) {
        return mapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteAll(List<String> idList) {
        return mapper.deleteBatchIds(idList) > 0;
    }

}
