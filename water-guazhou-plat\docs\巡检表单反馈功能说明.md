# 巡检表单反馈功能说明

## 功能概述

巡检表单反馈功能专注于展示和管理`sm_circuit_task_form_record`表中的巡检表单记录，提供表单完成情况的统计和详细的记录查看功能。

## 数据来源

### 主要数据表：`sm_circuit_task_form_record`
```sql
-- 表单记录表结构
CREATE TABLE sm_circuit_task_form_record (
    id VARCHAR(32) PRIMARY KEY,
    task_id VARCHAR(32) NOT NULL,
    task_code VARCHAR(50),
    inspection_config_id VARCHAR(32),
    form_item_id VARCHAR(10),
    location VARCHAR(100),
    position VARCHAR(100),
    content TEXT,
    result VARCHAR(20) DEFAULT 'NOT_CHECKED',
    result_description TEXT,
    attachments TEXT,
    check_time TIMESTAMP,
    check_user_id VARCHAR(32),
    create_time TIMESTAMP,
    tenant_id VARCHAR(32)
);
```

### 检验结果枚举值
- `NOT_CHECKED`：未检查
- `NORMAL`：正常
- `ABNORMAL`：异常
- `NEED_REPAIR`：需维修

## 功能模块

### 1. 反馈列表页面 (`InspectionFeedback.vue`)

#### 搜索条件
- **任务编号**：支持模糊搜索
- **任务名称**：支持模糊搜索
- **任务状态**：已完成、待审核、已驳回
- **执行人员**：执行人员筛选
- **完成时间**：日期范围选择

#### 列表字段
- 任务编号、任务名称
- 执行人员、协作人员
- 开始时间、结束时间
- 任务状态
- **表单完成率**：已检查项目/总项目数
- 创建时间

#### 操作功能
- **查看反馈**：打开表单记录详情弹窗
- **导出**：导出表单记录数据

### 2. 反馈详情弹窗 (`FeedbackDetailDialog.vue`)

#### 任务基本信息
显示选中任务的基础信息：
- 任务ID
- 任务状态
- 其他基本信息

#### 表单反馈统计
以卡片形式展示统计数据：
- **总检查项**：该任务的表单记录总数
- **已检查**：result不为'NOT_CHECKED'的记录数
- **未检查**：result为'NOT_CHECKED'的记录数
- **完成率**：已检查项目占总项目的百分比

#### 巡检表单记录列表
表格形式显示所有表单记录：
- **巡检地点**：location字段
- **巡检位置**：position字段
- **检验内容**：content字段
- **检验结果**：result字段（显示中文）
- **结果描述**：result_description字段
- **检验时间**：check_time字段
- **检验人员**：check_user_id字段

#### 操作功能
- **查看**：查看单条记录详情
- **编辑**：编辑记录（仅未检查的记录可编辑）
- **刷新**：重新加载表单记录

## 统计计算逻辑

### 完成率计算
```javascript
const calculateFormStats = (records) => {
  const totalItems = records.length
  const checkedItems = records.filter(r => r.result !== 'NOT_CHECKED').length
  const uncheckedItems = totalItems - checkedItems
  const completionRate = totalItems > 0 
    ? Math.round((checkedItems / totalItems) * 100)
    : 0
    
  return {
    totalItems,
    checkedItems,
    uncheckedItems,
    completionRate
  }
}
```

### 任务级别完成率
```javascript
// 在任务列表中显示的完成率
const taskCompletionRate = (task) => {
  const formRecords = getFormRecordsByTaskId(task.id)
  const stats = calculateFormStats(formRecords)
  return stats.completionRate
}
```

## API接口

### 1. 获取表单记录
```javascript
// 根据任务ID获取表单记录
GET /api/sm/circuitTaskFormRecord/task/{taskId}

// 响应格式
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "record_id",
      "taskId": "task_id",
      "taskCode": "202506090008",
      "inspectionConfigId": "1",
      "formItemId": "0",
      "location": "厂内生活区A区",
      "position": "雨调蓄井",
      "content": "井身是否有裂纹、沉降现象，井盖是否完好",
      "result": "NOT_CHECKED",
      "resultDescription": null,
      "attachments": null,
      "checkTime": null,
      "checkUserId": null,
      "createTime": 1749439807060,
      "tenantId": "tenant_id"
    }
  ]
}
```

### 2. 更新表单记录
```javascript
// 更新单条表单记录
PUT /api/sm/circuitTaskFormRecord/{id}

// 请求参数
{
  "result": "NORMAL",
  "resultDescription": "检查正常",
  "checkTime": "2024-06-09 15:30:00",
  "checkUserId": "user_id",
  "attachments": "attachment_urls"
}
```

## 数据处理

### 安全的数据获取
```javascript
const loadFormRecords = async () => {
  try {
    const res = await getFormRecordsByTaskId(props.taskId)
    
    // 处理不同的响应数据格式
    let responseData = res.data.data || res.data || []
    
    // 确保是对象时提取数组
    if (responseData && typeof responseData === 'object' && !Array.isArray(responseData)) {
      responseData = responseData.data || responseData.records || responseData.content || []
    }
    
    // 确保最终是数组
    const formRecords = Array.isArray(responseData) ? responseData : []
    
    // 设置表格数据
    FormRecordTableConfig.dataList = formRecords
    
    // 计算统计数据
    calculateFormStats(formRecords)
    
  } catch (error) {
    console.error('加载表单记录失败:', error)
    FormRecordTableConfig.dataList = []
  }
}
```

### 状态管理
```javascript
// 弹窗打开时初始化
watch(visible, (newVal) => {
  if (newVal && props.taskId) {
    // 清空数据
    FormRecordTableConfig.dataList = []
    resetStats()
    
    // 加载数据
    loadFormRecords()
  } else if (!newVal) {
    // 关闭时清空
    FormRecordTableConfig.dataList = []
    resetStats()
  }
})
```

## 用户交互

### 1. 查看反馈流程
1. 在巡检反馈列表页面选择任务
2. 点击"查看反馈"按钮
3. 弹窗显示任务信息和表单统计
4. 查看详细的表单记录列表
5. 可以查看或编辑单条记录

### 2. 编辑记录流程
1. 在表单记录列表中找到未检查的记录
2. 点击"编辑"按钮
3. 填写检验结果和描述
4. 保存更新
5. 刷新统计数据

### 3. 数据导出流程
1. 选择需要导出的任务
2. 点击"导出"按钮
3. 生成包含表单记录的报告
4. 下载导出文件

## 权限控制

### 查看权限
- 所有有权限访问巡检模块的用户都可以查看反馈

### 编辑权限
- 只有任务的执行人员和协作人员可以编辑表单记录
- 已检查的记录不能再次编辑（防止数据被误改）

### 导出权限
- 管理员和相关负责人可以导出数据

## 业务规则

### 1. 表单记录生成
- 当巡检计划生成任务时，如果关联了巡检配置，自动生成表单记录
- 每个表单配置项对应一条记录
- 初始状态为'NOT_CHECKED'

### 2. 记录更新规则
- 只能更新result、resultDescription、checkTime、checkUserId、attachments字段
- 更新时必须设置checkTime和checkUserId
- 一旦设置为非'NOT_CHECKED'状态，不能再改回'NOT_CHECKED'

### 3. 统计计算规则
- 完成率基于result字段计算
- 实时更新，每次数据变化后重新计算
- 百分比四舍五入到整数

## 扩展功能

### 1. 批量操作
- 批量标记为正常
- 批量导出选中记录
- 批量分配检验人员

### 2. 数据分析
- 按地点统计完成情况
- 按时间段分析检验趋势
- 异常项目汇总报告

### 3. 移动端支持
- 响应式设计适配移动设备
- 支持拍照上传附件
- 离线数据同步

通过以上设计，巡检表单反馈功能能够全面展示和管理表单记录，为巡检工作提供有效的数据支持和管理工具。
