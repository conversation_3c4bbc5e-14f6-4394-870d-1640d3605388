# 巡检任务表单记录功能说明

## 功能概述

本功能实现了巡检计划和配置表单的绑定，在用户完成巡检时需要填写配置表单中的内容。

## 主要组件

### 后端组件

1. **实体类**
   - `SMCircuitTaskFormRecord`: 巡检任务表单记录实体
   - 位置: `water-guazhou-plat/dao/src/main/java/org/thingsboard/server/dao/model/sql/smartManagement/plan/SMCircuitTaskFormRecord.java`

2. **服务层**
   - `SMCircuitTaskFormRecordService`: 服务接口
   - `SMCircuitTaskFormRecordServiceImpl`: 服务实现
   - 位置: `water-guazhou-plat/dao/src/main/java/org/thingsboard/server/dao/plan/`

3. **控制器**
   - `SMCircuitTaskFormRecordController`: REST API控制器
   - 位置: `water-guazhou-plat/application/src/main/java/org/thingsboard/server/controller/smartManagement/plan/`

4. **数据访问层**
   - `SMCircuitTaskFormRecordMapper`: MyBatis Mapper接口
   - `SMCircuitTaskFormRecordMapper.xml`: SQL映射文件

### 前端组件

1. **API接口**
   - 位置: `water-guazhou-ui/src/api/smartManagement/circuitTaskFormRecord.ts`

2. **UI组件**
   - 在 `PatrolDetail.vue` 中添加了"巡检表单"Tab
   - 位置: `water-guazhou-ui/src/views/arcMap/Inspection/Plans/components/PatrolDetail.vue`

## 数据库表结构

```sql
CREATE TABLE sm_circuit_task_form_record (
  id varchar(64) NOT NULL,
  task_id varchar(64) NOT NULL,
  task_code varchar(100) NOT NULL,
  inspection_config_id varchar(64) NOT NULL,
  form_item_id varchar(64) NOT NULL,
  location varchar(200),
  position varchar(200),
  content text,
  result varchar(50) DEFAULT 'NOT_CHECKED',
  result_description text,
  attachments text,
  check_time timestamp NULL,
  check_user_id varchar(64),
  create_time timestamp DEFAULT CURRENT_TIMESTAMP,
  tenant_id varchar(64) NOT NULL,
  PRIMARY KEY (id)
);
```

## 功能流程

### 1. 任务创建时生成表单记录

当巡检计划关联了巡检配置时，在创建巡检任务时会自动生成对应的表单记录：

- 在 `SMCircuitPlanServiceImpl.plan()` 方法中
- 根据巡检配置的 `formConfig` JSON 生成表单记录
- 每个表单项对应一条记录

### 2. 用户填写表单

在巡检任务详情页面：

- 点击"巡检表单"Tab
- 显示该任务的所有表单记录
- 点击"填写"按钮打开编辑对话框
- 用户可以选择检验结果、填写描述、上传附件

### 3. 任务完成检查

在任务完成时会检查表单是否全部填写完成：

- 修改了 `SMCircuitTaskServiceImpl.canBeComplete()` 方法
- 只有当所有表单记录都不是"未检查"状态时，任务才能完成

## API接口

### 1. 根据任务ID查询表单记录
```
GET /api/sm/circuitTaskFormRecord/task/{taskId}
```

### 2. 更新检验结果
```
PATCH /api/sm/circuitTaskFormRecord/{id}
```

### 3. 批量更新检验结果
```
POST /api/sm/circuitTaskFormRecord/batchUpdate
```

### 4. 检查任务表单是否全部完成
```
GET /api/sm/circuitTaskFormRecord/task/{taskId}/completed
```

## 配置要求

### 1. 巡检计划配置

巡检计划需要关联巡检配置：
- `SMCircuitPlan.inspectionConfigId` 字段需要设置为有效的巡检配置ID

### 2. 巡检配置

巡检配置需要包含表单配置：
- `CircuitSettings.formConfig` 字段需要包含有效的JSON格式表单配置

表单配置JSON格式示例：
```json
[
  {
    "location": "厂内生活区A区",
    "position": "雨调蓄井",
    "content": "井身是否有裂纹、沉降现象，井盖是否完好"
  },
  {
    "location": "厂内生活区A区", 
    "position": "引供水管线",
    "content": "压力、流量计设备是否完好，工作是否正常"
  }
]
```

## 测试

提供了测试页面用于验证功能：
- 位置: `water-guazhou-ui/src/views/test/FormRecordTest.vue`
- 可以测试API调用和表单记录的CRUD操作

## 注意事项

1. 确保巡检计划正确关联了巡检配置
2. 巡检配置的表单配置JSON格式必须正确
3. 用户权限需要包含表单记录的查看和编辑权限
4. 文件上传功能需要配置正确的文件存储路径

## 后续优化建议

1. 添加表单模板功能，支持更复杂的表单类型
2. 支持表单字段的动态配置
3. 添加表单数据的统计分析功能
4. 支持表单数据的导出功能
5. 添加表单填写的移动端支持
