# 巡检任务表单记录功能说明

## 功能概述

本功能实现了巡检计划和巡检配置的绑定，工作人员在执行巡检任务时需要根据巡检配置中的表单内容进行填写和记录。

## 业务流程

1. **配置阶段**：管理员在巡检配置(`CircuitSettings`)中设置表单配置(`formConfig`)，定义需要检查的地点、位置和内容
2. **计划阶段**：创建巡检计划(`SMCircuitPlan`)时关联相应的巡检配置(`inspectionConfigId`)
3. **任务生成**：系统根据巡检计划生成巡检任务时，自动为每个任务创建对应的表单记录
4. **执行阶段**：工作人员执行巡检任务时，在任务详情页面填写表单记录
5. **完成检查**：系统检查所有表单记录是否都已填写完成，才允许任务完成

## 主要组件

### 后端组件

1. **实体类**
   - `SMCircuitTaskFormRecord`: 巡检任务表单记录实体
   - 位置: `water-guazhou-plat/dao/src/main/java/org/thingsboard/server/dao/model/sql/smartManagement/plan/SMCircuitTaskFormRecord.java`

2. **请求类**
   - `SMCircuitTaskFormRecordPageRequest`: 分页查询请求
   - 位置: `water-guazhou-plat/dao/src/main/java/org/thingsboard/server/dao/util/imodel/query/smartManagement/plan/SMCircuitTaskFormRecordPageRequest.java`

3. **服务层**
   - `SMCircuitTaskFormRecordService`: 服务接口
   - `SMCircuitTaskFormRecordServiceImpl`: 服务实现
   - 位置: `water-guazhou-plat/dao/src/main/java/org/thingsboard/server/dao/plan/`

4. **控制器**
   - `SMCircuitTaskFormRecordController`: REST API控制器，使用 `@IStarController2` 注解
   - 位置: `water-guazhou-plat/application/src/main/java/org/thingsboard/server/controller/smartManagement/plan/`

5. **数据访问层**
   - `SMCircuitTaskFormRecordMapper`: MyBatis Mapper接口
   - `SMCircuitTaskFormRecordMapper.xml`: SQL映射文件

### 前端组件

1. **API接口**
   - 位置: `water-guazhou-ui/src/api/smartManagement/circuitTaskFormRecord.ts`

2. **UI组件**
   - 在 `PatrolDetail.vue` 中添加了"巡检表单"Tab
   - 位置: `water-guazhou-ui/src/views/arcMap/Inspection/Plans/components/PatrolDetail.vue`

## 数据库表结构

```sql
CREATE TABLE sm_circuit_task_form_record (
  id varchar(64) NOT NULL,
  task_id varchar(64) NOT NULL,
  task_code varchar(100) NOT NULL,
  inspection_config_id varchar(64) NOT NULL,
  form_item_id varchar(64) NOT NULL,
  location varchar(200),
  position varchar(200),
  content text,
  result varchar(50) DEFAULT 'NOT_CHECKED',
  result_description text,
  attachments text,
  check_time timestamp NULL,
  check_user_id varchar(64),
  create_time timestamp DEFAULT CURRENT_TIMESTAMP,
  tenant_id varchar(64) NOT NULL,
  PRIMARY KEY (id)
);
```

## 详细流程

### 1. 巡检配置设置

管理员在巡检配置管理页面创建巡检配置：

- 设置配置名称、编码、类型等基本信息
- 配置表单内容(`formConfig`)，JSON格式如下：
```json
[
  {
    "location": "厂内生活区A区",
    "position": "雨调蓄井",
    "content": "井身是否有裂纹、沉降现象，井盖是否完好"
  },
  {
    "location": "厂内生活区A区",
    "position": "引供水管线",
    "content": "压力、流量计设备是否完好，工作是否正常"
  }
]
```

### 2. 巡检计划关联配置

创建巡检计划时：

- 设置 `inspectionConfigId` 字段关联巡检配置
- 系统会根据关联的配置生成相应的表单记录

### 3. 任务创建时自动生成表单记录

当巡检计划生成任务时：

- 在 `SMCircuitPlanServiceImpl.plan()` 方法中
- 检查巡检计划是否关联了巡检配置
- 如果关联了配置，根据配置的 `formConfig` JSON 为每个任务生成表单记录
- 每个表单项对应一条 `SMCircuitTaskFormRecord` 记录

### 4. 工作人员填写表单

在巡检任务详情页面：

- 点击"巡检表单"Tab 查看需要填写的表单项
- 显示该任务的所有表单记录（地点、位置、检验内容）
- 点击"填写"按钮打开编辑对话框
- 工作人员选择检验结果（正常/异常）、填写结果描述、上传附件

### 5. 任务完成检查

在任务完成时会检查表单是否全部填写完成：

- 修改了 `SMCircuitTaskServiceImpl.canBeComplete()` 方法
- 调用 `formRecordService.isTaskFormCompleted()` 检查表单完成状态
- 只有当所有表单记录都不是"未检查"状态时，任务才能完成

## API接口

### 1. 分页查询表单记录
```
GET /api/sm/circuitTaskFormRecord
```

### 2. 根据任务ID查询表单记录
```
GET /api/sm/circuitTaskFormRecord/task/{taskId}
```

### 3. 根据任务编号查询表单记录
```
GET /api/sm/circuitTaskFormRecord/taskCode/{taskCode}
```

### 4. 根据ID查询表单记录
```
GET /api/sm/circuitTaskFormRecord/{id}
```

### 5. 保存表单记录
```
POST /api/sm/circuitTaskFormRecord
```

### 6. 更新表单记录
```
POST /api/sm/circuitTaskFormRecord/{id}
```

### 7. 删除表单记录
```
DELETE /api/sm/circuitTaskFormRecord/{id}
```

### 8. 更新检验结果
```
POST /api/sm/circuitTaskFormRecord/{id}/checkResult
```

### 9. 批量更新检验结果
```
POST /api/sm/circuitTaskFormRecord/batchUpdate
```

### 10. 检查任务表单是否全部完成
```
GET /api/sm/circuitTaskFormRecord/task/{taskId}/completed
```

## 使用说明

### 1. 创建巡检配置

在巡检配置管理页面：

1. 点击"新增"按钮
2. 填写配置名称、编码、类型等基本信息
3. 在表单配置区域添加检查项：
   - **巡检地点**：如"厂内生活区A区"
   - **巡检位置**：如"雨调蓄井"、"引供水管线"
   - **检验内容**：具体需要检查的内容描述
4. 保存配置

### 2. 创建巡检计划

在巡检计划制定页面（`/arcMap/Inspection/Plans/InspectPlan`）：

1. 选择区域、设备类别等基本信息
2. 设置计划参数（是否常规计划、是否需要反馈、进行方式、计划周期）
3. 输入计划名称
4. **选择巡检配置**：在"巡检配置"下拉框中选择已创建的巡检配置（可选）
5. 填写备注信息
6. 点击"确定"保存计划

**注意**：
- 巡检配置是可选的，如果不选择则不会生成表单记录
- 只有启用状态的巡检配置才会在下拉框中显示
- 下拉框显示格式为：`配置名称 (配置编码)`

### 3. 生成巡检任务

当巡检计划生成任务时：

- 系统会自动检查计划是否关联了巡检配置
- 如果关联了配置，会为每个任务自动生成对应的表单记录
- 表单记录包含配置中定义的所有检查项

### 4. 执行巡检任务

工作人员在巡检任务详情页面：

1. 点击"巡检表单"Tab
2. 查看所有需要检查的项目
3. 对每个项目点击"填写"按钮
4. 选择检验结果（正常/异常）
5. 填写结果描述（可选）
6. 上传相关附件（可选）
7. 保存填写结果

### 5. 完成任务

- 只有当所有表单项都已填写完成时，任务才能标记为完成
- 系统会自动检查表单完成状态

## 测试

### 1. 数据库测试数据

运行测试SQL脚本：
```sql
-- 位置: water-guazhou-plat/sql/test_circuit_settings_data.sql
-- 该脚本会创建测试用的巡检配置数据
```

### 2. 前端测试页面

提供了专门的测试页面：

- **综合测试页面**: `water-guazhou-ui/src/views/test/InspectionFormTest.vue`
  - 测试巡检配置的查看和解析
  - 测试表单记录的生成和填写
  - 模拟完整的业务流程

- **API测试页面**: `water-guazhou-ui/src/views/test/FormRecordTest.vue`
  - 测试表单记录的CRUD操作
  - 验证API接口的正确性

### 3. 测试步骤

1. **准备测试数据**
   ```bash
   # 在数据库中执行测试数据脚本
   psql -d your_database -f water-guazhou-plat/sql/test_circuit_settings_data.sql
   ```

2. **测试巡检配置**
   - 访问巡检配置管理页面
   - 查看测试配置的表单配置JSON
   - 验证配置数据的正确性

3. **测试巡检计划**
   - 创建新的巡检计划
   - 关联测试用的巡检配置
   - 生成巡检任务

4. **测试表单记录**
   - 在任务详情页面查看"巡检表单"Tab
   - 验证表单记录是否正确生成
   - 测试表单填写和保存功能

5. **测试任务完成**
   - 填写所有表单项
   - 验证任务是否可以正常完成

## 注意事项

1. 确保巡检计划正确关联了巡检配置
2. 巡检配置的表单配置JSON格式必须正确
3. 用户权限需要包含表单记录的查看和编辑权限
4. 文件上传功能需要配置正确的文件存储路径

## 后续优化建议

1. 添加表单模板功能，支持更复杂的表单类型
2. 支持表单字段的动态配置
3. 添加表单数据的统计分析功能
4. 支持表单数据的导出功能
5. 添加表单填写的移动端支持
