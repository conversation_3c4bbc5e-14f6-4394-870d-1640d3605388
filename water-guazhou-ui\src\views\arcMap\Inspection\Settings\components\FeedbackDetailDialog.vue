<template>
  <el-dialog
    v-model="visible"
    title="巡检反馈详情"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="feedback-detail">
      <!-- 任务基本信息 -->
      <el-card class="task-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>任务基本信息</span>
          </div>
        </template>
        <div class="task-info">
          <p><strong>任务ID:</strong> {{ taskId }}</p>
          <p><strong>状态:</strong> 加载中...</p>
        </div>
      </el-card>

      <!-- 表单反馈统计 -->
      <el-card class="feedback-stats-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>表单反馈统计</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ formStats.totalItems }}</div>
              <div class="stat-label">总检查项</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ formStats.checkedItems }}</div>
              <div class="stat-label">已检查</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ formStats.uncheckedItems }}</div>
              <div class="stat-label">未检查</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ formStats.completionRate }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 表单记录详情 -->
      <el-card class="form-records-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>巡检表单记录</span>
            <el-button
              type="primary"
              size="small"
              @click="refreshFormRecords"
              :loading="loading"
            >
              刷新
            </el-button>
          </div>
        </template>
        <FormTable :config="FormRecordTableConfig"></FormTable>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, shallowRef } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, View } from '@element-plus/icons-vue'
import FormTable from '@/components/Form/FormTable.vue'
import { formatDate } from '@/utils/DateFormatter'
import { getFormRecordsByTaskId } from '@/api/smartManagement/circuitTaskFormRecord'

interface Props {
  modelValue: boolean
  taskId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 表单统计
const formStats = reactive({
  totalItems: 0,
  checkedItems: 0,
  uncheckedItems: 0,
  completionRate: 0
})

// 表单记录表格配置
const FormRecordTableConfig = reactive<ITable>({
  title: '',
  height: '400px',
  indexVisible: true,
  columns: [
    { minWidth: 120, label: '巡检地点', prop: 'location' },
    { minWidth: 120, label: '巡检位置', prop: 'position' },
    { minWidth: 200, label: '检验内容', prop: 'content' },
    {
      minWidth: 100,
      label: '检验结果',
      prop: 'result',
      formatter: (row: any) => {
        const resultMap: Record<string, string> = {
          'NOT_CHECKED': '未检查',
          'NORMAL': '正常',
          'ABNORMAL': '异常',
          'NEED_REPAIR': '需维修'
        }
        return resultMap[row.result] || row.result
      }
    },
    { minWidth: 150, label: '结果描述', prop: 'resultDescription' },
    {
      minWidth: 160,
      label: '检验时间',
      prop: 'checkTime',
      formatter: (row: any) => formatDate(row.checkTime, 'YYYY-MM-DD HH:mm:ss')
    },
    {
      minWidth: 120,
      label: '检验人员',
      prop: 'checkUserId'
    }
  ],
  dataList: [],
  loading: false,
  pagination: { hide: true },
  operations: [
    {
      perm: true,
      text: '查看',
      isTextBtn: true,
      svgIcon: shallowRef(View),
      click: (row: any) => handleViewRecord(row)
    },
    {
      perm: true,
      text: '编辑',
      isTextBtn: true,
      svgIcon: shallowRef(Edit),
      click: (row: any) => handleEditRecord(row),
      disabled: (row: any) => row.result !== 'NOT_CHECKED' // 已检查的不能编辑
    }
  ]
})

// 加载表单记录
const loadFormRecords = async () => {
  if (!props.taskId) return

  try {
    loading.value = true
    FormRecordTableConfig.loading = true

    const res = await getFormRecordsByTaskId(props.taskId)

    if (res?.data) {
      // 处理不同的响应数据格式
      let responseData = res.data.data || res.data || []

      // 如果responseData是对象且包含data属性，则取data属性
      if (responseData && typeof responseData === 'object' && !Array.isArray(responseData)) {
        responseData = responseData.data || responseData.records || responseData.content || []
      }

      // 确保formRecords是数组
      const formRecords = Array.isArray(responseData) ? responseData : []
      FormRecordTableConfig.dataList = formRecords

      // 计算统计数据
      calculateFormStats(formRecords)

      console.log('表单记录数据:', formRecords)
    }
  } catch (error) {
    console.error('加载表单记录失败:', error)
    ElMessage.error('加载表单记录失败')
    // 设置为空数组，避免后续处理出错
    FormRecordTableConfig.dataList = []
  } finally {
    loading.value = false
    FormRecordTableConfig.loading = false
  }
}

// 计算表单统计数据
const calculateFormStats = (records: any[]) => {
  formStats.totalItems = records.length
  formStats.checkedItems = records.filter(r => r.result !== 'NOT_CHECKED').length
  formStats.uncheckedItems = formStats.totalItems - formStats.checkedItems
  formStats.completionRate = formStats.totalItems > 0
    ? Math.round((formStats.checkedItems / formStats.totalItems) * 100)
    : 0
}

// 刷新表单记录
const refreshFormRecords = () => {
  loadFormRecords()
}

// 查看记录详情
const handleViewRecord = (row: any) => {
  ElMessage.info('查看记录详情功能开发中...')
}

// 编辑记录
const handleEditRecord = (row: any) => {
  ElMessage.info('编辑记录功能开发中...')
}

// 导出报告
const handleExport = () => {
  ElMessage.info('导出报告功能开发中...')
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal && props.taskId) {
    // 初始化表格数据为空数组
    FormRecordTableConfig.dataList = []

    // 重置统计数据
    formStats.totalItems = 0
    formStats.checkedItems = 0
    formStats.uncheckedItems = 0
    formStats.completionRate = 0

    // 加载表单记录
    loadFormRecords()
  } else if (!newVal) {
    // 关闭弹窗时清空数据
    FormRecordTableConfig.dataList = []
    formStats.totalItems = 0
    formStats.checkedItems = 0
    formStats.uncheckedItems = 0
    formStats.completionRate = 0
  }
})
</script>

<style lang="scss" scoped>
.feedback-detail {
  .task-info-card,
  .feedback-stats-card,
  .form-records-card {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
  }

  .task-info {
    padding: 16px;

    p {
      margin: 8px 0;
    }
  }

  .stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #409eff;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
