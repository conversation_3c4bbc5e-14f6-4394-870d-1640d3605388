<template>
  <el-dialog
    v-model="visible"
    title="巡检反馈详情"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="feedback-detail">
      <!-- 任务基本信息 -->
      <el-card class="task-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>任务基本信息</span>
          </div>
        </template>
        <div class="task-info">
          <p><strong>任务ID:</strong> {{ taskId }}</p>
          <p><strong>状态:</strong> 开发中...</p>
        </div>
      </el-card>

      <!-- 反馈统计 -->
      <el-card class="feedback-stats-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>反馈统计</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ feedbackStats.totalReports }}</div>
              <div class="stat-label">总报告数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ feedbackStats.feedbackReports }}</div>
              <div class="stat-label">已反馈</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ feedbackStats.pendingReports }}</div>
              <div class="stat-label">待反馈</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ feedbackStats.feedbackRate }}%</div>
              <div class="stat-label">反馈率</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  modelValue: boolean
  taskId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 反馈统计
const feedbackStats = reactive({
  totalReports: 0,
  feedbackReports: 0,
  pendingReports: 0,
  feedbackRate: 0
})

// 导出报告
const handleExport = () => {
  ElMessage.info('导出报告功能开发中...')
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal && props.taskId) {
    // 模拟加载数据
    feedbackStats.totalReports = 5
    feedbackStats.feedbackReports = 3
    feedbackStats.pendingReports = 2
    feedbackStats.feedbackRate = 60
  } else if (!newVal) {
    // 关闭弹窗时清空数据
    feedbackStats.totalReports = 0
    feedbackStats.feedbackReports = 0
    feedbackStats.pendingReports = 0
    feedbackStats.feedbackRate = 0
  }
})
</script>

<style lang="scss" scoped>
.feedback-detail {
  .task-info-card,
  .feedback-stats-card {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-header {
    font-weight: 600;
    font-size: 16px;
  }

  .task-info {
    padding: 16px;

    p {
      margin: 8px 0;
    }
  }

  .stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #409eff;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
