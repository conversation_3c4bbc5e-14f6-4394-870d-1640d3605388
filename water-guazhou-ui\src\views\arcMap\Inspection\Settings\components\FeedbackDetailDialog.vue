<template>
  <el-dialog
    v-model="visible"
    title="巡检反馈详情"
    width="80%"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="feedback-detail">
      <!-- 任务基本信息 -->
      <el-card class="task-info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>任务基本信息</span>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="任务编号">{{ taskInfo.code }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getStatusType(taskInfo.status)">
              {{ getStatusText(taskInfo.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行人员">{{ taskInfo.receiveUserName }}</el-descriptions-item>
          <el-descriptions-item label="协作人员">{{ taskInfo.collaborateUserName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(taskInfo.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDate(taskInfo.beginTime) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ formatDate(taskInfo.endTime) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="3">{{ taskInfo.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 反馈统计 -->
      <el-card class="feedback-stats-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>反馈统计</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ feedbackStats.totalReports }}</div>
              <div class="stat-label">总报告数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ feedbackStats.feedbackReports }}</div>
              <div class="stat-label">已反馈</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ feedbackStats.pendingReports }}</div>
              <div class="stat-label">待反馈</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ feedbackStats.feedbackRate }}%</div>
              <div class="stat-label">反馈率</div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 反馈报告列表 -->
      <el-card class="feedback-reports-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>反馈报告列表</span>
          </div>
        </template>
        <FormTable :config="ReportTableConfig"></FormTable>
      </el-card>

      <!-- 表单记录列表 -->
      <el-card class="form-records-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>表单记录</span>
          </div>
        </template>
        <FormTable :config="FormRecordTableConfig"></FormTable>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed, shallowRef } from 'vue'
import { ElMessage } from 'element-plus'
import { View, Download } from '@element-plus/icons-vue'
import FormTable from '@/components/Form/FormTable.vue'
import { formatDate } from '@/utils/DateFormatter'
import { PatrolTaskStatusConfig } from '@/views/arcMap/Inspection/config'
import { GetPatrolReport } from '@/api/patrol/gis/plan'
import { getFormRecordsByTaskId } from '@/api/smartManagement/circuitTaskFormRecord'

interface Props {
  modelValue: boolean
  taskId: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 任务信息
const taskInfo = ref<any>({})

// 反馈统计
const feedbackStats = reactive({
  totalReports: 0,
  feedbackReports: 0,
  pendingReports: 0,
  feedbackRate: 0
})

// 反馈报告表格配置
const ReportTableConfig = reactive<ITable>({
  title: '',
  height: '300px',
  indexVisible: true,
  columns: [
    { minWidth: 120, label: '报告类型', prop: 'type' },
    { minWidth: 150, label: '关键点/设备', prop: 'name' },
    { minWidth: 100, label: '是否到位', prop: 'isSettle', formatter: (row: any) => row.isSettle ? '是' : '否' },
    { minWidth: 100, label: '是否反馈', prop: 'isFallback', formatter: (row: any) => row.isFallback ? '是' : '否' },
    { minWidth: 160, label: '到位时间', prop: 'settleTime', formatter: (row: any) => formatDate(row.settleTime) },
    { minWidth: 160, label: '反馈时间', prop: 'fallbackTime', formatter: (row: any) => formatDate(row.fallbackTime) }
  ],
  dataList: [],
  loading: false,
  pagination: { hide: true },
  operations: [
    {
      perm: true,
      text: '查看',
      isTextBtn: true,
      svgIcon: shallowRef(View),
      click: (row: any) => handleViewReport(row)
    }
  ]
})

// 表单记录表格配置
const FormRecordTableConfig = reactive<ITable>({
  title: '',
  height: '300px',
  indexVisible: true,
  columns: [
    { minWidth: 120, label: '巡检地点', prop: 'location' },
    { minWidth: 120, label: '巡检位置', prop: 'position' },
    { minWidth: 200, label: '检验内容', prop: 'content' },
    { minWidth: 100, label: '检验结果', prop: 'result', formatter: (row: any) => getResultText(row.result) },
    { minWidth: 150, label: '结果描述', prop: 'resultDescription' },
    { minWidth: 160, label: '检验时间', prop: 'checkTime', formatter: (row: any) => formatDate(row.checkTime) }
  ],
  dataList: [],
  loading: false,
  pagination: { hide: true }
})

// 获取状态类型
const getStatusType = (status: string) => {
  const config = PatrolTaskStatusConfig[status as keyof typeof PatrolTaskStatusConfig]
  return config?.type || ''
}

// 获取状态文本
const getStatusText = (status: string) => {
  const config = PatrolTaskStatusConfig[status as keyof typeof PatrolTaskStatusConfig]
  return config?.text || status
}

// 获取结果文本
const getResultText = (result: string) => {
  const resultMap: Record<string, string> = {
    'NOT_CHECKED': '未检查',
    'NORMAL': '正常',
    'ABNORMAL': '异常',
    'NEED_REPAIR': '需维修'
  }
  return resultMap[result] || result
}

// 加载任务详情
const loadTaskDetail = async () => {
  if (!props.taskId) return

  try {
    // 这里应该调用获取任务详情的API
    // 暂时使用模拟数据
    taskInfo.value = {
      id: props.taskId,
      code: 'TASK-2024-001',
      name: '水厂日常巡检',
      status: 'APPROVED',
      receiveUserName: '张三',
      collaborateUserName: '李四',
      createTime: new Date(),
      beginTime: new Date(),
      endTime: new Date(),
      remark: '正常巡检任务'
    }
  } catch (error) {
    console.error('加载任务详情失败:', error)
    ElMessage.error('加载任务详情失败')
  }
}

// 加载反馈报告
const loadFeedbackReports = async () => {
  if (!props.taskId) return

  try {
    ReportTableConfig.loading = true
    const res = await GetPatrolReport({
      taskCode: taskInfo.value.code,
      page: 1,
      size: 100
    })

    if (res?.data?.code === 200) {
      // 处理不同的响应数据格式
      let responseData = res.data.data || res.data || []

      // 如果responseData是对象且包含data属性，则取data属性
      if (responseData && typeof responseData === 'object' && !Array.isArray(responseData)) {
        responseData = responseData.data || responseData.records || responseData.content || []
      }

      // 确保reports是数组
      const reports = Array.isArray(responseData) ? responseData : []
      ReportTableConfig.dataList = reports

      // 计算统计数据
      feedbackStats.totalReports = reports.length
      feedbackStats.feedbackReports = reports.filter((r: any) => r.isFallback).length
      feedbackStats.pendingReports = feedbackStats.totalReports - feedbackStats.feedbackReports
      feedbackStats.feedbackRate = feedbackStats.totalReports > 0
        ? Math.round((feedbackStats.feedbackReports / feedbackStats.totalReports) * 100)
        : 0

      console.log('反馈报告数据:', reports)
    }
  } catch (error) {
    console.error('加载反馈报告失败:', error)
    ElMessage.error('加载反馈报告失败')
    // 设置为空数组，避免后续处理出错
    ReportTableConfig.dataList = []
  } finally {
    ReportTableConfig.loading = false
  }
}

// 加载表单记录
const loadFormRecords = async () => {
  if (!props.taskId) return

  try {
    FormRecordTableConfig.loading = true
    const res = await getFormRecordsByTaskId(props.taskId)

    if (res?.data) {
      // 处理不同的响应数据格式
      let responseData = res.data.data || res.data || []

      // 如果responseData是对象且包含data属性，则取data属性
      if (responseData && typeof responseData === 'object' && !Array.isArray(responseData)) {
        responseData = responseData.data || responseData.records || responseData.content || []
      }

      // 确保formRecords是数组
      const formRecords = Array.isArray(responseData) ? responseData : []
      FormRecordTableConfig.dataList = formRecords

      console.log('表单记录数据:', formRecords)
    }
  } catch (error) {
    console.error('加载表单记录失败:', error)
    ElMessage.error('加载表单记录失败')
    // 设置为空数组，避免后续处理出错
    FormRecordTableConfig.dataList = []
  } finally {
    FormRecordTableConfig.loading = false
  }
}

// 查看报告详情
const handleViewReport = (row: any) => {
  ElMessage.info('查看报告详情功能开发中...')
}

// 导出报告
const handleExport = () => {
  ElMessage.info('导出报告功能开发中...')
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal && props.taskId) {
    // 初始化表格数据为空数组
    ReportTableConfig.dataList = []
    FormRecordTableConfig.dataList = []

    loadTaskDetail()
    loadFeedbackReports()
    loadFormRecords()
  } else if (!newVal) {
    // 关闭弹窗时清空数据
    ReportTableConfig.dataList = []
    FormRecordTableConfig.dataList = []
    taskInfo.value = {}
    feedbackStats.totalReports = 0
    feedbackStats.feedbackReports = 0
    feedbackStats.pendingReports = 0
    feedbackStats.feedbackRate = 0
  }
})
</script>

<style lang="scss" scoped>
.feedback-detail {
  .task-info-card,
  .feedback-stats-card,
  .feedback-reports-card,
  .form-records-card {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  .card-header {
    font-weight: 600;
    font-size: 16px;
  }

  .stat-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    .stat-value {
      font-size: 24px;
      font-weight: 600;
      color: #409eff;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
