package org.thingsboard.server.dao.util.imodel.query.smartManagement.plan;

import lombok.Getter;
import lombok.Setter;
import org.thingsboard.server.dao.model.sql.smartManagement.plan.SMCircuitTaskFormRecord;
import org.thingsboard.server.dao.util.imodel.query.SaveRequest;
import org.thingsboard.server.dao.util.imodel.query.annotations.NotNullOrEmpty;

import java.util.Date;

/**
 * 巡检任务表单记录保存请求
 */
@Getter
@Setter
public class SMCircuitTaskFormRecordSaveRequest extends SaveRequest<SMCircuitTaskFormRecord> {

    /**
     * 任务ID
     */
    @NotNullOrEmpty
    private String taskId;

    /**
     * 任务编号
     */
    @NotNullOrEmpty
    private String taskCode;

    /**
     * 巡检配置ID
     */
    @NotNullOrEmpty
    private String inspectionConfigId;

    /**
     * 表单项ID
     */
    @NotNullOrEmpty
    private String formItemId;

    /**
     * 巡检地点
     */
    private String location;

    /**
     * 巡检位置
     */
    private String position;

    /**
     * 检验内容
     */
    private String content;

    /**
     * 检验结果
     */
    private String result;

    /**
     * 结果描述
     */
    private String resultDescription;

    /**
     * 附件
     */
    private String attachments;

    /**
     * 检验时间
     */
    private Date checkTime;

    /**
     * 检验人员ID
     */
    private String checkUserId;

    @Override
    protected SMCircuitTaskFormRecord build() {
        SMCircuitTaskFormRecord entity = new SMCircuitTaskFormRecord();
        entity.setCreateTime(createTime());
        entity.setTenantId(tenantId());
        commonSet(entity);
        return entity;
    }

    @Override
    public SMCircuitTaskFormRecord update(String id) {
        SMCircuitTaskFormRecord entity = new SMCircuitTaskFormRecord();
        entity.setId(id);
        updateSet(entity);
        return entity;
    }

    protected void commonSet(SMCircuitTaskFormRecord entity) {
        entity.setTaskId(taskId);
        entity.setTaskCode(taskCode);
        entity.setInspectionConfigId(inspectionConfigId);
        entity.setFormItemId(formItemId);
        entity.setLocation(location);
        entity.setPosition(position);
        entity.setContent(content);
        entity.setResult(result != null ? result : "NOT_CHECKED");
        entity.setResultDescription(resultDescription);
        entity.setAttachments(attachments);
        entity.setCheckTime(checkTime);
        entity.setCheckUserId(checkUserId);
    }

    private void updateSet(SMCircuitTaskFormRecord entity) {
        entity.setResult(result);
        entity.setResultDescription(resultDescription);
        entity.setAttachments(attachments);
        entity.setCheckTime(checkTime != null ? checkTime : new Date());
        entity.setCheckUserId(checkUserId);
    }
}
